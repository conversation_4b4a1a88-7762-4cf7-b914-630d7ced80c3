// ==================== 全局配置 ====================
const CONFIG = {
  EXCLUDE_SHEETS: [
    '汇总',
    '明细',
    '代理汇总',
    '昨日消耗',
    '*透视*',
    '*_备份'
  ],
  PIVOT_TABLE: {
    TABLE_NAME: "昨日消耗_数据透视表",
    ROWS: [
      {field: '项目', sortOrder: '升序', showTotals: true, repeatLabels: true},
      {field: '包', sortOrder: '升序', showTotals: true, repeatLabels: true}
    ],
    COLUMNS: [
      {field: '使用人', sortOrder: '升序', showTotals: true}
    ],
    VALUES: [
      {field: '消耗', summarizeFunction: 'SUM'}
    ],
    UI: {
      backgroundColor: '#e6f4ea',
      fontFamily: 'Arial',
      fontSize: 11,
      fontColor: '#202124'
    }
  }
};

// ==================== 主菜单设置 ====================
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  const menu = ui.createMenu('数据工具');
  
  // 核心功能
  menu.addItem('合并工作表', 'safeMergeSheets');
  menu.addItem('转换汇总表', 'convertTotalSheet');
  menu.addItem('转换当前表', 'convertCurrentSheet');
  menu.addSeparator();
  
  // 分析功能
  menu.addItem('代理汇总', 'addAgentColumnAndSummary');
  menu.addItem('📊 昨日包消耗汇总', 'summarizeYesterdayPackageConsumption');
  menu.addItem('📈 创建透视表', 'safeCreatePivotTable');
  menu.addSeparator();
  
  // 高级功能
  menu.addItem('🚀 一键处理', 'safeOneClickProcess');
  menu.addSeparator();
  menu.addItem('🔄 初始化', 'initializeSheet');
  menu.addSeparator();
  menu.addItem('🔧 修复账户ID科学计数法', 'fixAccountIdScientificNotation');
  menu.addItem('⚙️ 设置账户ID为文本格式', 'setAccountIdColumnAsText');
  menu.addItem('🔍 检测账户ID格式', 'checkAccountIdFormat');
  menu.addItem('列信息', 'showColumnInfo');
  
  menu.addToUi();
}

// ==================== 工作表合并功能 ====================
function safeMergeSheets() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheets = ss.getSheets();
    
    // 过滤要合并的工作表
    const toMerge = sheets.filter(s => {
      const name = s.getName();
      
      // 检查精确排除
      if (CONFIG.EXCLUDE_SHEETS.includes(name)) return false;
      
      // 检查通配符排除
      for (const pattern of CONFIG.EXCLUDE_SHEETS) {
        if (pattern.includes('*')) {
          const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
          if (regex.test(name)) return false;
        }
      }

      return true;
    });
    
    if (toMerge.length < 2) {
      SpreadsheetApp.getUi().alert('提示', '可合并的表少于2个', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }
    
    // 显示合并预览
    const sheetNames = toMerge.map(s => s.getName()).join('\n');
    const shouldMerge = SpreadsheetApp.getUi().alert(
      '合并预览',
      `将要合并的工作表:\n\n${sheetNames}\n\n是否继续?`,
      SpreadsheetApp.getUi().ButtonSet.YES_NO
    ) === SpreadsheetApp.getUi().Button.YES;
    
    if (!shouldMerge) return;
    
    // 合并工作表数据
    let mergedData = [];
    let maxColumns = 0;
    
    toMerge.forEach((sheet, index) => {
      const data = getSheetDataWithAccountIdFix(sheet);
      
      if (index === 0) {
        // 包含第一张表的完整数据
        mergedData = [...data];
      } else {
        // 只包含后续表的数据部分（不包括表头）
        mergedData = [...mergedData, ...data.slice(1)];
      }
      
      // 更新最大列数
      maxColumns = Math.max(maxColumns, ...data.map(row => row.length));
    });
    
    // 填充空单元格使所有行等长
    mergedData = mergedData.map(row => {
      while (row.length < maxColumns) {
        row.push('');
      }
      return row;
    });
    
    // 创建/更新汇总表
    const summarySheet = getOrCreateSheet(ss, '汇总');
    summarySheet.clear();

    // 检查数据是否为空
    if (mergedData.length === 0 || mergedData[0].length === 0) {
      throw new Error('合并后的数据为空');
    }

    // 🔑 关键修复：在写入数据之前先设置账户ID列为文本格式
    const headers = mergedData[0];
    const accountIdIndex = headers.findIndex(header =>
      header && (String(header).includes('账户ID') || String(header).includes('ID'))
    );
    if (accountIdIndex !== -1) {
      // 预先设置整个账户ID列为文本格式，防止写入时自动转换为科学计数法
      const columnLetter = String.fromCharCode(65 + accountIdIndex);
      const columnRange = summarySheet.getRange(`${columnLetter}:${columnLetter}`);
      columnRange.setNumberFormat('@');
      console.log(`预设置账户ID列（第${accountIdIndex + 1}列）为文本格式`);
    }

    // 现在安全地写入数据，不会被自动转换为科学计数法
    summarySheet.getRange(1, 1, mergedData.length, mergedData[0].length).setValues(mergedData);
    
    SpreadsheetApp.getUi().alert('完成', `成功合并 ${toMerge.length} 个工作表, 共 ${mergedData.length} 行数据`, SpreadsheetApp.getUi().ButtonSet.OK);
  } catch (e) {
    handleError('合并工作表失败', e);
  }
}

// ==================== 数据转换功能 ====================
function convertTotalSheet() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const targetSheet = findTotalSheet(ss.getSheets());
    
    if (!targetSheet) {
      SpreadsheetApp.getUi().alert('错误', '未找到含"总"字样的表', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }
    
    processSheet(targetSheet);
  } catch (e) {
    handleError('转换汇总表失败', e);
  }
}

function convertCurrentSheet() {
  try {
    processSheet(SpreadsheetApp.getActiveSpreadsheet().getActiveSheet());
  } catch (e) {
    handleError('转换当前表失败', e);
  }
}

function processSheet(sheet) {
  const data = getSheetDataWithAccountIdFix(sheet);
  
  if (data.length === 0) {
    throw new Error('工作表为空');
  }
  
  const structure = analyzeTableStructure(data);
  const converted = convertToLongFormat(data, structure);
  
  const detailSheet = getOrCreateSheet(SpreadsheetApp.getActiveSpreadsheet(), '明细');
  detailSheet.clear();

  // 检查转换后的数据是否为空
  if (converted.length > 0 && converted[0].length > 0) {
    detailSheet.getRange(1, 1, converted.length, converted[0].length).setValues(converted);
  } else {
    throw new Error('转换后的数据为空');
  }
  
  // 设置账户 ID 列为文本格式
  const detailHeaders = converted[0];
  const accountIdHeader = structure.identityColumns.find(c => c.name === '账户ID')?.originalName;
  if (accountIdHeader) {
    const colIndex = detailHeaders.indexOf(accountIdHeader) + 1;
    if (colIndex > 0) {
      detailSheet.getRange(1, colIndex, converted.length, 1).setNumberFormat('@');
    }
  }
  
  SpreadsheetApp.getUi().alert('完成', `成功转换 ${converted.length} 行数据`, SpreadsheetApp.getUi().ButtonSet.OK);
}

function analyzeTableStructure(data) {
  // 增加边界检查
  if (!data || data.length === 0 || !data[0]) {
    throw new Error('数据为空或格式不正确');
  }

  const headers = data[0];
  
  // 识别关键列
  const keyColumns = [
    { type: 'identity', name: '投放平台-代理', keys: ['投放平台', '代理'] },
    { type: 'identity', name: '项目', keys: ['项目'] },
    { type: 'identity', name: '包', keys: ['包'] },
    { type: 'identity', name: '账户名', keys: ['账户名', '账户'] },
    { type: 'identity', name: '账户ID', keys: ['账户ID', 'ID'] },
    { type: 'identity', name: '使用人', keys: ['使用人', '用户'] },
    { type: 'date', pattern: /(\d{2})\.(\d{2})消耗\$/ }
  ];
  
  const result = {
    identityColumns: [],
    dateColumns: []
  };
  
  keyColumns.forEach(config => {
    if (config.type === 'identity') {
      for (const key of config.keys) {
        const index = headers.findIndex(h => h && String(h).includes(key));
        if (index !== -1) {
          result.identityColumns.push({
            name: config.name,
            index: index,
            originalName: headers[index]
          });
          break;
        }
      }
    } else if (config.type === 'date') {
      headers.forEach((header, index) => {
        if (header) { // 增加空值检查
          const match = String(header).match(config.pattern);
          if (match) {
            result.dateColumns.push({
              name: header,
              index: index,
              date: `${new Date().getFullYear()}/${match[1]}/${match[2]}`
            });
          }
        }
      });
    }
  });
  
  return result;
}

function convertToLongFormat(data, structure) {
  const { identityColumns, dateColumns } = structure;
  
  if (identityColumns.length === 0 || dateColumns.length === 0) {
    throw new Error('缺少必要的关键列');
  }
  
  // 创建表头
  const headers = ['日期', ...identityColumns.map(c => c.originalName), '消耗'];
  const hasAgent = identityColumns.some(c => c.name === '投放平台-代理');
  if (hasAgent) headers.push('代理');
  
  const result = [headers];
  
  // 处理每一行数据
  for (let rowIndex = 1; rowIndex < data.length; rowIndex++) {
    const row = data[rowIndex];
    
    // 清理标识列的值（增强边界检查）
    identityColumns.forEach(col => {
      if (row && col.index < row.length) {
        row[col.index] = cleanStringValue(row[col.index]);
      }
    });
    
    // 处理每个日期列（增强边界检查）
    dateColumns.forEach(dateCol => {
      if (row && dateCol.index < row.length) {
        const value = cleanNumberValue(row[dateCol.index]);

        // 只处理有效值
        if (value !== null && value > 0) {
          // 创建新行（增强边界检查）
          const newRow = [
            dateCol.date,
            ...identityColumns.map(col => (row && col.index < row.length) ? row[col.index] : ''),
            value
          ];

          // 如果需要代理列
          if (hasAgent) {
            const agentIndex = identityColumns.findIndex(col => col.name === '投放平台-代理');
            if (agentIndex !== -1) {
              const agentCol = identityColumns[agentIndex];
              const agentValue = (row && agentCol.index < row.length) ? row[agentCol.index] : '';
              newRow.push(extractAgent(agentValue));
            }
          }

          result.push(newRow);
        }
      }
    });
  }
  
  return result;
}

// ==================== 代理汇总功能 ====================
function addAgentColumnAndSummary() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const detailSheet = ss.getSheetByName('明细');
    
    if (!detailSheet) {
      throw new Error('未找到"明细"表');
    }
    
    const data = getSheetDataWithAccountIdFix(detailSheet);

    if (data.length < 2) {
      throw new Error('明细表无数据');
    }
    
    // 定位代理列
    const headers = data[0];
    const agentIndex = headers.findIndex(h =>
      h && (String(h).includes('投放平台-代理') || String(h).includes('代理'))
    );
    
    if (agentIndex === -1) {
      throw new Error('未找到代理列');
    }
    
    // 生成汇总数据
    const summary = generateAgentDailySummary(data, agentIndex);
    
    // 创建/更新代理汇总表
    const summarySheet = getOrCreateSheet(ss, '代理汇总');
    summarySheet.clear();
    summarySheet.getRange(1, 1, summary.length, summary[0].length).setValues(summary);
    
    SpreadsheetApp.getUi().alert('完成', `生成了 ${summary.length - 1} 行代理汇总数据`, SpreadsheetApp.getUi().ButtonSet.OK);
  } catch (e) {
    handleError('代理汇总失败', e);
  }
}

function generateAgentDailySummary(data, agentIndex) {
  // 增加边界检查
  if (!data || data.length === 0 || !data[0]) {
    throw new Error('数据为空或格式不正确');
  }

  const headers = data[0];
  const dateIndex = 0;
  const valueIndex = headers.findIndex(h => h && String(h).includes('消耗'));
  
  const summaryMap = {};
  
  // 处理每一行数据
  for (let i = 1; i < data.length; i++) {
    const row = data[i];
    
    // 格式化日期
    let dateValue;
    if (row[dateIndex] instanceof Date) {
      dateValue = Utilities.formatDate(row[dateIndex], Session.getScriptTimeZone(), 'yyyy/MM/dd');
    } else {
      dateValue = String(row[dateIndex]).trim();
      
      // 尝试解析日期字符串
      try {
        const parsedDate = new Date(dateValue);
        if (!isNaN(parsedDate)) {
          dateValue = Utilities.formatDate(parsedDate, Session.getScriptTimeZone(), 'yyyy/MM/dd');
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
    
    // 提取代理名称
    const agentValue = extractAgent(String(row[agentIndex]));
    
    // 提取消耗值
    let value = cleanNumberValue(row[valueIndex]);
    
    // 跳过无效行
    if (!dateValue || !agentValue || value === null || value <= 0) continue;
    
    // 更新汇总数据
    const key = `${dateValue}_${agentValue}`;
    if (!summaryMap[key]) {
      summaryMap[key] = { date: dateValue, agent: agentValue, total: 0 };
    }
    summaryMap[key].total += value;
  }
  
  // 创建汇总表
  const result = [['日期', '代理', '总消耗']];
  
  Object.values(summaryMap)
    .sort((a, b) => {
      // 按日期排序
      const dateCompare = a.date.localeCompare(b.date);
      if (dateCompare !== 0) return dateCompare;
      
      // 按代理名称排序
      return a.agent.localeCompare(b.agent);
    })
    .forEach(item => {
      // 四舍五入到小数点后两位
      const roundedTotal = Math.round(item.total * 100) / 100;
      result.push([item.date, item.agent, roundedTotal]);
    });
  
  return result;
}

// ==================== 昨日消耗汇总功能 ====================
function summarizeYesterdayPackageConsumption() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const detailSheet = ss.getSheetByName('明细');
    
    if (!detailSheet) {
      throw new Error('未找到"明细"表');
    }
    
    const data = getSheetDataWithAccountIdFix(detailSheet);

    if (data.length < 2) {
      throw new Error('明细表无数据');
    }
    
    // 定位需要的列
    const headers = data[0];
    const dateIndex = headers.findIndex(h => h && String(h).includes('日期'));
    const userIndex = headers.findIndex(h => h && (String(h).includes('使用人') || String(h).includes('用户')));
    const projectIndex = headers.findIndex(h => h && String(h).includes('项目'));
    const packageIndex = headers.findIndex(h => h && String(h).includes('包'));
    const valueIndex = headers.findIndex(h => h && String(h).includes('消耗'));
    
    if ([dateIndex, userIndex, projectIndex, packageIndex, valueIndex].includes(-1)) {
      const missing = [];
      if (dateIndex === -1) missing.push('日期');
      if (userIndex === -1) missing.push('使用人');
      if (projectIndex === -1) missing.push('项目');
      if (packageIndex === -1) missing.push('包');
      if (valueIndex === -1) missing.push('消耗');
      
      throw new Error(`缺少必要的列: ${missing.join(', ')}`);
    }
    
    // 计算昨天的日期（修复：正确处理月初日期）
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayFormatted = Utilities.formatDate(yesterday, Session.getScriptTimeZone(), 'yyyy/MM/dd');
    
    // 过滤出昨天的数据
    const yesterdayRows = [];
    
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      if (row[dateIndex] === null || row[dateIndex] === undefined) continue;
      
      let rowDate;
      
      if (row[dateIndex] instanceof Date) {
        rowDate = Utilities.formatDate(row[dateIndex], Session.getScriptTimeZone(), 'yyyy/MM/dd');
      } else {
        try {
          const parsedDate = new Date(row[dateIndex]);
          if (isNaN(parsedDate)) {
            rowDate = String(row[dateIndex]).trim();
          } else {
            rowDate = Utilities.formatDate(parsedDate, Session.getScriptTimeZone(), 'yyyy/MM/dd');
          }
        } catch (e) {
          rowDate = String(row[dateIndex]).trim();
        }
      }
      
      if (rowDate === yesterdayFormatted) {
        yesterdayRows.push(row);
      }
    }
    
    if (yesterdayRows.length === 0) {
      SpreadsheetApp.getUi().alert('提示', `昨天(${yesterdayFormatted})没有数据`, SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }
    
    // 汇总数据
    const summaryMap = {};
    
    yesterdayRows.forEach(row => {
      const user = cleanStringValue(row[userIndex]);
      const project = cleanStringValue(row[projectIndex]);
      const package = cleanStringValue(row[packageIndex]);
      const value = cleanNumberValue(row[valueIndex]) || 0;
      
      const key = `${user}||${project}||${package}`;
      
      if (!summaryMap[key]) {
        summaryMap[key] = {
          date: yesterdayFormatted,
          user: user,
          project: project,
          package: package,
          total: 0
        };
      }
      
      summaryMap[key].total += value;
    });
    
    // 创建结果
    const result = [['日期', '使用人', '项目', '包', '消耗']];
    
    Object.values(summaryMap)
      .sort((a, b) => {
        // 先按使用人排序
        const userCompare = a.user.localeCompare(b.user);
        if (userCompare !== 0) return userCompare;
        
        // 然后按项目排序
        const projectCompare = a.project.localeCompare(b.project);
        if (projectCompare !== 0) return projectCompare;
        
        // 最后按包排序
        return a.package.localeCompare(b.package);
      })
      .forEach(item => {
        // 四舍五入到小数点后两位
        const roundedTotal = Math.round(item.total * 100) / 100;
        result.push([item.date, item.user, item.project, item.package, roundedTotal]);
      });
    
    // 创建/更新昨日消耗表
    const yesterdaySheet = getOrCreateSheet(ss, '昨日消耗');
    yesterdaySheet.clear();
    yesterdaySheet.getRange(1, 1, result.length, result[0].length).setValues(result);
    
    SpreadsheetApp.getUi().alert('完成', `生成了 ${result.length - 1} 行昨日消耗数据`, SpreadsheetApp.getUi().ButtonSet.OK);
  } catch (e) {
    handleError('昨日消耗汇总失败', e);
  }
}

// ==================== 数据透视表功能（严格匹配图片设置） ====================
function safeCreatePivotTable() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    
    // 检查源数据表
    const sourceSheet = ss.getSheetByName('昨日消耗');
    if (!sourceSheet) {
      throw new Error('请先运行"昨日包消耗汇总"生成数据');
    }
    
    const sourceData = sourceSheet.getDataRange();
    const sourceValues = sourceData.getValues();
    
    if (sourceValues.length < 2) {
      throw new Error('"昨日消耗"表中没有足够的数据');
    }
    
    const headers = sourceValues[0];
    
    // 安全删除旧透视表
    const pivotSheetName = CONFIG.PIVOT_TABLE.TABLE_NAME;
    deleteSheetWithRetry(ss, pivotSheetName);
    
    // 创建新透视表
    const pivotSheet = ss.insertSheet(pivotSheetName);
    
    // 创建透视表（从A2开始）
    const pivotRange = pivotSheet.getRange('A2');
    const pivotTable = pivotRange.createPivotTable(sourceData);
    
    // 获取列索引（严格匹配图片设置，增强边界检查）
    const getColumnIndex = (field) => {
      if (!headers || headers.length === 0) {
        throw new Error('表头数据为空');
      }

      for (let i = 0; i < headers.length; i++) {
        if (headers[i] && String(headers[i]).trim() === field) {
          return i + 1;
        }
      }
      throw new Error(`未找到列: ${field}`);
    };
    
    // === 设置行分组 ===
    const rowFields = CONFIG.PIVOT_TABLE.ROWS;
    
    for (const fieldConfig of rowFields) {
      const columnIndex = getColumnIndex(fieldConfig.field);
      const rowGroup = pivotTable.addRowGroup(columnIndex);
      
      // 设置排序
      if (rowGroup.setSortOrder) {
        if (fieldConfig.sortOrder === '升序') {
          rowGroup.setSortOrder(SpreadsheetApp.SortOrder.ASCENDING);
        } else {
          rowGroup.setSortOrder(SpreadsheetApp.SortOrder.DESCENDING);
        }
      }
      
      // 设置总计显示
      if (rowGroup.setShowTotals && fieldConfig.showTotals !== undefined) {
        rowGroup.setShowTotals(fieldConfig.showTotals);
      }
      
      // 设置标签重复
      if (rowGroup.setRepeatLabels && fieldConfig.repeatLabels !== undefined) {
        rowGroup.setRepeatLabels(fieldConfig.repeatLabels);
      }
    }
    
    // === 设置列分组 ===
    const columnFields = CONFIG.PIVOT_TABLE.COLUMNS;
    
    for (const fieldConfig of columnFields) {
      const columnIndex = getColumnIndex(fieldConfig.field);
      const columnGroup = pivotTable.addColumnGroup(columnIndex);
      
      // 设置排序
      if (columnGroup.setSortOrder) {
        if (fieldConfig.sortOrder === '升序') {
          columnGroup.setSortOrder(SpreadsheetApp.SortOrder.ASCENDING);
        } else {
          columnGroup.setSortOrder(SpreadsheetApp.SortOrder.DESCENDING);
        }
      }
      
      // 设置总计显示
      if (columnGroup.setShowTotals && fieldConfig.showTotals !== undefined) {
        columnGroup.setShowTotals(fieldConfig.showTotals);
      }
    }
    
    // === 设置值区域 ===
    const valueFields = CONFIG.PIVOT_TABLE.VALUES;
    
    for (const fieldConfig of valueFields) {
      const columnIndex = getColumnIndex(fieldConfig.field);
      const summarizeFunction = SpreadsheetApp.PivotTableSummarizeFunction.SUM;
      
      pivotTable.addPivotValue(columnIndex, summarizeFunction);
    }
    
    // === 添加图片中的UI元素 ===
    // 表标题
    pivotSheet.getRange('A1').setValue(CONFIG.PIVOT_TABLE.TABLE_NAME);
    
    // 应用样式
    applyPivotTableStyles(pivotSheet);
    
    // 调整列宽
    Utilities.sleep(500); // 等待透视表渲染
    pivotSheet.autoResizeColumns(1, pivotSheet.getLastColumn());
    
    SpreadsheetApp.getActiveSpreadsheet().toast('透视表创建完成', '成功', 5);
    return pivotSheet;
  } catch (e) {
    handleError('创建透视表失败', e);
    return null;
  }
}

function applyPivotTableStyles(sheet) {
  const style = CONFIG.PIVOT_TABLE.UI;
  
  // 标题行样式
  const titleRow = sheet.getRange('1:1');
  titleRow
    .setBackground('#ffffff')
    .setFontFamily(style.fontFamily)
    .setFontSize(style.fontSize)
    .setFontWeight('normal');
  
  // 透视表标题样式
  sheet.getRange('A1')
    .setFontWeight('bold')
    .setFontSize(style.fontSize + 2);
  
  // 透视表表头样式
  const headerRange = sheet.getRange('A2:Z2');
  headerRange
    .setBackground(style.backgroundColor)
    .setFontFamily(style.fontFamily)
    .setFontSize(style.fontSize)
    .setFontWeight('bold');
  
  // 设置边框
  headerRange.setBorder(true, false, false, false, false, false);
  
  // 数据区域样式
  const dataRange = sheet.getDataRange();
  dataRange
    .setFontFamily(style.fontFamily)
    .setFontSize(style.fontSize);
  
  // 表名字段样式
  sheet.getRange('A1:D1')
    .setFontColor(style.fontColor)
    .setHorizontalAlignment('left');
}

function deleteSheetWithRetry(ss, sheetName, retries = 3, delay = 500) {
  for (let i = 0; i < retries; i++) {
    try {
      const sheet = ss.getSheetByName(sheetName);
      if (sheet) {
        ss.deleteSheet(sheet);
      }
      return;
    } catch (e) {
      if (i === retries - 1) throw e;
      Utilities.sleep(delay);
    }
  }
}

// 批量修复所有工作表的账户ID科学计数法问题（用于一键处理）
function fixAllSheetsAccountId() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheets = ss.getSheets();

    // 过滤需要处理的工作表（包括汇总表，因为它可能也有科学计数法问题）
    const sheetsToFix = sheets.filter(s => {
      const name = s.getName();

      // 检查精确排除（但允许汇总表）
      const excludeList = CONFIG.EXCLUDE_SHEETS.filter(item => item !== '汇总');
      if (excludeList.includes(name)) return false;

      // 检查通配符排除
      for (const pattern of excludeList) {
        if (pattern.includes('*')) {
          const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
          if (regex.test(name)) return false;
        }
      }

      return true;
    });

    if (sheetsToFix.length === 0) {
      console.log('没有需要修复的工作表');
      return;
    }

    let totalFixed = 0;
    let totalUnfixable = 0;
    const processedSheets = [];

    // 处理每个工作表
    sheetsToFix.forEach(sheet => {
      const result = fixAccountIdInSheet(sheet);
      if (result.hasAccountId) {
        processedSheets.push({
          name: sheet.getName(),
          fixed: result.fixedCount,
          unfixable: result.unfixableCount
        });
        totalFixed += result.fixedCount;
        totalUnfixable += result.unfixableCount;
      }
    });

    // 显示汇总结果
    if (processedSheets.length > 0) {
      console.log(`账户ID修复完成: 处理了 ${processedSheets.length} 个工作表, 修复 ${totalFixed} 个单元格, ${totalUnfixable} 个需要手动修复`);
    } else {
      console.log('未发现包含账户ID的工作表');
    }

  } catch (e) {
    console.error('批量修复账户ID失败:', e.message);
    // 改进错误处理：记录错误但允许流程继续
    SpreadsheetApp.getActiveSpreadsheet().toast(
      `账户ID修复过程中出现警告: ${e.message}`,
      '警告',
      5
    );
  }
}

// 修复单个工作表的账户ID（辅助函数）
function fixAccountIdInSheet(sheet) {
  try {
    const range = sheet.getDataRange();
    const data = range.getValues();
    const displayData = range.getDisplayValues();

    if (data.length === 0) {
      return { hasAccountId: false, fixedCount: 0, unfixableCount: 0 };
    }

    const headers = displayData[0];

    // 查找账户ID列
    const accountIdIndex = headers.findIndex(header =>
      String(header).includes('账户ID') || String(header).includes('ID')
    );

    if (accountIdIndex === -1) {
      return { hasAccountId: false, fixedCount: 0, unfixableCount: 0 };
    }

    // 检测和修复科学计数法
    const scientificNotationPattern = /^\d+\.?\d*[eE][+-]?\d+$/;
    let fixedCount = 0;
    let unfixableCount = 0;
    let hasChanges = false;

    for (let i = 1; i < data.length; i++) {
      // 增强边界检查：确保行和列都存在
      if (data[i] && accountIdIndex < data[i].length &&
          displayData[i] && accountIdIndex < displayData[i].length) {
        const rawValue = data[i][accountIdIndex];
        const displayValue = String(displayData[i][accountIdIndex]).trim();

        // 如果显示值是科学计数法
        if (scientificNotationPattern.test(displayValue)) {
          // 尝试从原始值恢复
          if (typeof rawValue === 'number' && Number.isInteger(rawValue)) {
            // 如果原始值是整数，可以安全转换
            data[i][accountIdIndex] = rawValue.toString();
            fixedCount++;
            hasChanges = true;
          } else {
            // 无法安全转换的情况
            unfixableCount++;
          }
        }
      }
    }

    // 设置列格式为文本并写入修复后的数据
    if (accountIdIndex !== -1) {
      const columnLetter = String.fromCharCode(65 + accountIdIndex);
      const columnRange = sheet.getRange(`${columnLetter}:${columnLetter}`);
      columnRange.setNumberFormat('@');

      if (hasChanges) {
        range.setValues(data);
      }
    }

    return {
      hasAccountId: true,
      fixedCount: fixedCount,
      unfixableCount: unfixableCount
    };

  } catch (e) {
    console.error(`修复工作表 ${sheet.getName()} 失败:`, e.message);
    return { hasAccountId: false, fixedCount: 0, unfixableCount: 0 };
  }
}

// ==================== 初始化功能 ====================
function initializeSheet() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheets = ss.getSheets();

    // 过滤要初始化的工作表（排除系统表和汇总表）
    const toInitialize = sheets.filter(s => {
      const name = s.getName();

      // 检查精确排除
      if (CONFIG.EXCLUDE_SHEETS.includes(name)) return false;

      // 检查通配符排除
      for (const pattern of CONFIG.EXCLUDE_SHEETS) {
        if (pattern.includes('*')) {
          const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
          if (regex.test(name)) return false;
        }
      }

      return true;
    });

    if (toInitialize.length === 0) {
      SpreadsheetApp.getUi().alert('提示', '没有找到需要初始化的工作表', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }

    // 显示初始化预览
    const sheetNames = toInitialize.map(s => s.getName()).join('\n');
    const shouldProceed = SpreadsheetApp.getUi().alert(
      '批量初始化确认',
      `即将对以下 ${toInitialize.length} 个工作表执行初始化操作：\n\n${sheetNames}\n\n操作内容：\n1. 清空 J2:AN273 区域的内容\n2. 更新月份标识（上月→本月）\n\n是否继续？`,
      SpreadsheetApp.getUi().ButtonSet.YES_NO
    ) === SpreadsheetApp.getUi().Button.YES;

    if (!shouldProceed) {
      return;
    }

    // 获取当前日期并计算上月和本月
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // getMonth() 返回 0-11，需要加1
    const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1;

    // 格式化为两位数字符串
    const currentMonthStr = currentMonth.toString().padStart(2, '0');
    const lastMonthStr = lastMonth.toString().padStart(2, '0');

    let totalReplacementCount = 0;
    const processedSheets = [];

    // 处理每个工作表
    toInitialize.forEach(sheet => {
      try {
        const sheetName = sheet.getName();

        // 步骤1：清空指定区域（动态计算范围，避免硬编码）
        const lastRow = Math.max(273, sheet.getLastRow());
        const lastCol = Math.max(40, sheet.getLastColumn()); // AN列是第40列
        const startCol = 10; // J列是第10列

        if (lastRow >= 2 && lastCol >= startCol) {
          const targetRange = sheet.getRange(2, startCol, lastRow - 1, lastCol - startCol + 1);
          targetRange.clear();
          console.log(`${sheetName}: 已清空 J2:${String.fromCharCode(64 + lastCol)}${lastRow} 区域`);
        } else {
          console.log(`${sheetName}: 工作表太小，跳过清空操作`);
        }

        // 步骤2：执行正则替换
        const dataRange = sheet.getDataRange();
        const values = dataRange.getValues();
        let replacementCount = 0;

        // 构建正则表达式：匹配 "上月.XX消耗$" 格式（修复：正确转义点号）
        const regex = new RegExp(`${lastMonthStr}(\\.\\d{2}消耗\\$\\s*)`, 'g');
        const replacement = `${currentMonthStr}$1`;

        // 遍历所有单元格进行替换（增强边界检查）
        for (let row = 0; row < values.length; row++) {
          if (values[row]) { // 确保行存在
            for (let col = 0; col < values[row].length; col++) {
              const cellValue = values[row][col];
              if (typeof cellValue === 'string' && cellValue.includes('消耗$')) {
                const newValue = cellValue.replace(regex, replacement);
                if (newValue !== cellValue) {
                  values[row][col] = newValue;
                  replacementCount++;
                }
              }
            }
          }
        }

        // 如果有替换，更新工作表
        if (replacementCount > 0) {
          dataRange.setValues(values);
          console.log(`${sheetName}: 完成月份替换，共替换 ${replacementCount} 个单元格`);
        }

        totalReplacementCount += replacementCount;
        processedSheets.push({
          name: sheetName,
          replacements: replacementCount
        });

      } catch (e) {
        console.error(`初始化工作表 ${sheet.getName()} 失败:`, e.message);
        // 继续处理其他工作表，不中断整个流程
      }
    });

    // 显示完成信息
    const successCount = processedSheets.length;
    const detailInfo = processedSheets
      .map(s => `${s.name}: ${s.replacements} 个替换`)
      .join('\n');

    SpreadsheetApp.getUi().alert(
      '批量初始化完成',
      `批量初始化操作已完成！\n\n✅ 成功处理 ${successCount} 个工作表\n✅ 月份标识更新：${lastMonthStr} → ${currentMonthStr}\n✅ 总共替换 ${totalReplacementCount} 个单元格\n\n详细信息：\n${detailInfo}`,
      SpreadsheetApp.getUi().ButtonSet.OK
    );

  } catch (e) {
    handleError('批量初始化失败', e);
  }
}

// ==================== 一键处理流程 ====================
function safeOneClickProcess() {
  const startTime = new Date();
  const steps = [
    {name: '修复账户ID科学计数法', func: fixAllSheetsAccountId},
    {name: '合并工作表', func: safeMergeSheets},
    {name: '转换汇总表', func: convertTotalSheet},
    {name: '代理汇总', func: addAgentColumnAndSummary},
    {name: '昨日消耗汇总', func: summarizeYesterdayPackageConsumption},
    {name: '创建透视表', func: safeCreatePivotTable}
  ];
  
  try {
    for (const step of steps) {
      const stepStart = new Date();
      
      // 执行步骤
      step.func();
      
      // 计算步骤耗时
      const stepDuration = (new Date() - stepStart) / 1000;
      
      // 显示进度
      SpreadsheetApp.getActiveSpreadsheet().toast(
        `✓ ${step.name} 完成 (耗时 ${stepDuration.toFixed(1)}秒)`,
        '处理进度',
        5
      );
    }
    
    // 计算总耗时
    const totalDuration = (new Date() - startTime) / 1000;
    
    // 显示完成信息
    SpreadsheetApp.getUi().alert(
      '处理完成',
      `所有步骤成功执行！\n总耗时: ${totalDuration.toFixed(1)}秒`,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  } catch (e) {
    handleError('一键处理中断', e);
  }
}

// ==================== 辅助函数 ====================
function getOrCreateSheet(ss, sheetName) {
  let sheet = ss.getSheetByName(sheetName);

  if (sheet) {
    sheet.clear();
  } else {
    sheet = ss.insertSheet(sheetName);
  }

  return sheet;
}

function getSheetData(sheet) {
  return sheet.getDataRange().getDisplayValues();
}

// 智能数据读取函数，检测并自动修复科学计数法问题（性能优化版本）
function getSheetDataWithAccountIdFix(sheet) {
  const range = sheet.getDataRange();
  const rawValues = range.getValues();
  const displayValues = range.getDisplayValues();

  if (displayValues.length === 0) return [];

  // 查找账户ID列（增强边界检查）
  const headers = displayValues[0];
  if (!headers || headers.length === 0) return displayValues;

  const accountIdIndex = headers.findIndex(header =>
    header && (String(header).includes('账户ID') || String(header).includes('ID'))
  );

  // 如果没有找到账户ID列，返回显示值
  if (accountIdIndex === -1) {
    return displayValues;
  }

  // 检测账户ID列是否包含科学计数法
  const scientificNotationDetected = detectScientificNotationInColumn(displayValues, accountIdIndex);

  if (scientificNotationDetected.hasScientificNotation) {
    console.log(`检测到工作表 "${sheet.getName()}" 中的账户ID科学计数法，正在自动修复...`);

    // 自动修复科学计数法
    const scientificNotationPattern = /^\d+\.?\d*[eE][+-]?\d+$/;
    let fixedCount = 0;
    let unfixableCount = 0;

    // 创建修复后的数据副本
    const fixedData = displayValues.map(row => [...row]);

    for (let i = 1; i < displayValues.length; i++) {
      // 增强边界检查：确保行和列都存在
      if (displayValues[i] && accountIdIndex < displayValues[i].length &&
          rawValues[i] && accountIdIndex < rawValues[i].length) {
        const displayValue = String(displayValues[i][accountIdIndex]).trim();
        const rawValue = rawValues[i][accountIdIndex];

        // 如果显示值是科学计数法
        if (scientificNotationPattern.test(displayValue)) {
          // 尝试从原始值恢复
          if (typeof rawValue === 'number' && Number.isInteger(rawValue)) {
            // 如果原始值是整数，可以安全转换
            fixedData[i][accountIdIndex] = rawValue.toString();
            fixedCount++;
          } else {
            // 无法安全转换的情况，保持原显示值
            unfixableCount++;
          }
        }
      }
    }

    if (fixedCount > 0) {
      console.log(`工作表 "${sheet.getName()}" 自动修复了 ${fixedCount} 个账户ID`);

      // 设置账户ID列为文本格式并更新数据
      const columnLetter = String.fromCharCode(65 + accountIdIndex);
      const columnRange = sheet.getRange(`${columnLetter}:${columnLetter}`);
      columnRange.setNumberFormat('@');

      // 更新工作表数据（性能优化：只更新修改的数据）
      const updatedValues = rawValues.map((row, index) => {
        if (index === 0 || !row) return row; // 保持表头不变，跳过空行
        return row.map((cell, colIndex) => {
          if (colIndex === accountIdIndex && fixedData[index] && fixedData[index][colIndex] !== displayValues[index][colIndex]) {
            return fixedData[index][colIndex];
          }
          return cell;
        });
      });
      range.setValues(updatedValues);

      return fixedData;
    }

    if (unfixableCount > 0) {
      console.log(`工作表 "${sheet.getName()}" 有 ${unfixableCount} 个账户ID无法自动修复，需要手动处理`);
    }
  }

  return displayValues;
}

// 检测列中是否包含科学计数法
function detectScientificNotationInColumn(data, columnIndex) {
  const scientificNotationPattern = /^\d+\.?\d*[eE][+-]?\d+$/;
  const examples = [];
  let hasScientificNotation = false;

  for (let i = 1; i < data.length && examples.length < 3; i++) {
    // 增强边界检查：确保行和列都存在
    if (data[i] && columnIndex < data[i].length) {
      const value = String(data[i][columnIndex]).trim();
      if (scientificNotationPattern.test(value)) {
        hasScientificNotation = true;
        examples.push(`行 ${i + 1}: ${value}`);
      }
    }
  }

  return {
    hasScientificNotation,
    examples
  };
}

// 修复账户ID列的科学计数法问题
function fixAccountIdScientificNotation() {
  try {
    const sheet = SpreadsheetApp.getActiveSheet();
    const range = sheet.getDataRange();
    const data = range.getValues();
    const displayData = range.getDisplayValues();

    if (data.length === 0) {
      SpreadsheetApp.getUi().alert('提示', '当前工作表为空', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }

    const headers = displayData[0];

    // 查找账户ID列
    const accountIdIndex = headers.findIndex(header =>
      String(header).includes('账户ID') || String(header).includes('ID')
    );

    if (accountIdIndex === -1) {
      SpreadsheetApp.getUi().alert('提示', '未找到账户ID列', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }

    // 检测科学计数法并尝试修复
    const scientificNotationPattern = /^\d+\.?\d*[eE][+-]?\d+$/;
    const fixedCells = [];
    const unfixableCells = [];

    for (let i = 1; i < data.length; i++) {
      // 增强边界检查：确保行和列都存在
      if (data[i] && accountIdIndex < data[i].length &&
          displayData[i] && accountIdIndex < displayData[i].length) {
        const rawValue = data[i][accountIdIndex];
        const displayValue = String(displayData[i][accountIdIndex]).trim();

        // 如果显示值是科学计数法
        if (scientificNotationPattern.test(displayValue)) {
          // 尝试从原始值恢复
          if (typeof rawValue === 'number' && Number.isInteger(rawValue)) {
            // 如果原始值是整数，可以安全转换
            const fixedValue = rawValue.toString();
            data[i][accountIdIndex] = fixedValue;
            fixedCells.push(`行 ${i + 1}: ${displayValue} → ${fixedValue}`);
          } else {
            // 无法安全转换的情况
            unfixableCells.push(`行 ${i + 1}: ${displayValue} (需要手动修复)`);
          }
        }
      }
    }

    if (fixedCells.length === 0 && unfixableCells.length === 0) {
      SpreadsheetApp.getUi().alert('提示', '未发现科学计数法格式的账户ID', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }

    // 设置整列为文本格式
    const columnLetter = String.fromCharCode(65 + accountIdIndex);
    const columnRange = sheet.getRange(`${columnLetter}:${columnLetter}`);
    columnRange.setNumberFormat('@');

    // 写入修复后的数据
    if (fixedCells.length > 0) {
      range.setValues(data);
    }

    // 显示修复结果
    let message = `账户ID列修复完成！\n\n`;

    if (fixedCells.length > 0) {
      message += `✅ 成功修复 ${fixedCells.length} 个单元格：\n`;
      message += fixedCells.slice(0, 5).join('\n');
      if (fixedCells.length > 5) {
        message += `\n... 还有 ${fixedCells.length - 5} 个`;
      }
      message += '\n\n';
    }

    if (unfixableCells.length > 0) {
      message += `⚠️ 需要手动修复 ${unfixableCells.length} 个单元格：\n`;
      message += unfixableCells.slice(0, 5).join('\n');
      if (unfixableCells.length > 5) {
        message += `\n... 还有 ${unfixableCells.length - 5} 个`;
      }
      message += '\n\n请从原始数据源复制正确的完整账户ID。';
    }

    SpreadsheetApp.getUi().alert('修复结果', message, SpreadsheetApp.getUi().ButtonSet.OK);

  } catch (e) {
    handleError('修复账户ID失败', e);
  }
}

// 预防性设置：将账户ID列设置为文本格式
function setAccountIdColumnAsText() {
  try {
    const sheet = SpreadsheetApp.getActiveSheet();
    const range = sheet.getDataRange();
    const headers = range.getDisplayValues()[0];

    // 查找账户ID列
    const accountIdIndex = headers.findIndex(header =>
      String(header).includes('账户ID') || String(header).includes('ID')
    );

    if (accountIdIndex === -1) {
      SpreadsheetApp.getUi().alert('提示', '未找到账户ID列', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }

    // 设置整列为文本格式
    const columnLetter = String.fromCharCode(65 + accountIdIndex);
    const columnRange = sheet.getRange(`${columnLetter}:${columnLetter}`);
    columnRange.setNumberFormat('@');

    SpreadsheetApp.getUi().alert(
      '设置完成',
      `已将 ${headers[accountIdIndex]} 列（第${accountIdIndex + 1}列）设置为文本格式。\n\n✅ 现在可以安全地输入长账户ID，不会变成科学计数法。\n\n⚠️ 注意：此操作只影响新输入的数据，现有的科学计数法数据需要使用"修复账户ID科学计数法"功能处理。`,
      SpreadsheetApp.getUi().ButtonSet.OK
    );
  } catch (e) {
    handleError('设置账户ID格式失败', e);
  }
}

function findTotalSheet(sheets) {
  for (const sheet of sheets) {
    const name = sheet.getName();
    
    if (name.includes('总') || 
        name.includes('合并') || 
        name.includes('全部') ||
        name.toLowerCase().includes('total')) {
      return sheet;
    }
  }
  
  return null;
}

function cleanStringValue(value) {
  if (value == null) return '';
  
  const str = String(value).trim();
  
  // 处理常见空值表示
  if (str === '-' || str === '--' || str === 'N/A') return '';
  
  return str;
}

function cleanNumberValue(value) {
  if (value == null) return null;
  
  const str = String(value).trim();
  
  if (str === '') return null;
  
  // 移除货币符号、千分位分隔符等
  const cleaned = str.replace(/[^\d.-]/g, '');
  
  // 尝试转换
  const num = Number(cleaned);
  
  // 检查是否为有效数字
  return isNaN(num) ? null : num;
}

function extractAgent(value) {
  if (!value) return '';
  
  const str = String(value).trim();
  
  // 尝试识别 "-代理名" 模式
  const dashIndex = str.lastIndexOf('-');
  if (dashIndex !== -1) {
    return str.substring(dashIndex + 1).trim();
  }
  
  // 尝试识别 "代理：代理名" 模式
  const colonIndex = str.lastIndexOf('：');
  if (colonIndex !== -1) {
    return str.substring(colonIndex + 1).trim();
  }
  
  return str;
}

function handleError(context, error) {
  // 构建错误信息
  let errorMessage = `${context}: ${error.message || '未知错误'}`;
  
  // 添加堆栈信息（开发环境）
  if (error.stack) {
    console.error(error.stack);
  }
  
  // 用户友好的提示
  const userMessage = `
  发生错误: ${context}
  
  原因: ${error.message || '未知原因'}
  
  建议:
  1. 检查输入数据是否符合要求
  2. 确保所有必要的表格存在
  3. 如果问题持续，请尝试重新运行
  `;
  
  // 显示错误对话框
  SpreadsheetApp.getUi().alert(
    context,
    userMessage.trim(),
    SpreadsheetApp.getUi().ButtonSet.OK
  );
}

// ==================== 调试功能 ====================
function showColumnInfo() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const data = getSheetData(sheet);
  if (data.length === 0) {
    SpreadsheetApp.getUi().alert('提示', '当前工作表为空', SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }

  // 增加边界检查
  if (!data[0]) {
    SpreadsheetApp.getUi().alert('提示', '表头数据为空', SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }

  const headers = data[0];
  const info = headers.map((header, index) => `列 ${index + 1}: ${header || '(空)'}`).join('\n');
  SpreadsheetApp.getUi().alert('列信息', info, SpreadsheetApp.getUi().ButtonSet.OK);
}

// 检测当前工作表的账户ID格式问题
function checkAccountIdFormat() {
  try {
    const sheet = SpreadsheetApp.getActiveSheet();
    const data = sheet.getDataRange().getDisplayValues();

    if (data.length === 0) {
      SpreadsheetApp.getUi().alert('提示', '当前工作表为空', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }

    // 查找账户ID列
    const headers = data[0];
    const accountIdIndex = headers.findIndex(header =>
      String(header).includes('账户ID') || String(header).includes('ID')
    );

    if (accountIdIndex === -1) {
      SpreadsheetApp.getUi().alert('提示', '未找到账户ID列', SpreadsheetApp.getUi().ButtonSet.OK);
      return;
    }

    // 检测科学计数法
    const detection = detectScientificNotationInColumn(data, accountIdIndex);

    let message;
    if (detection.hasScientificNotation) {
      message = `❌ 检测到账户ID格式问题！\n\n问题数据：\n${detection.examples.join('\n')}\n\n建议使用"🔧 修复账户ID格式"功能。`;
    } else {
      // 检查是否已经是文本格式
      const columnLetter = String.fromCharCode(65 + accountIdIndex);
      const sampleCell = sheet.getRange(`${columnLetter}2`);
      const numberFormat = sampleCell.getNumberFormat();

      if (numberFormat === '@') {
        message = `✅ 账户ID列格式正常！\n\n列名：${headers[accountIdIndex]}\n格式：文本格式\n状态：无科学计数法问题`;
      } else {
        message = `⚠️ 账户ID列未设置为文本格式\n\n列名：${headers[accountIdIndex]}\n当前格式：${numberFormat}\n建议：使用"🔧 修复账户ID格式"预防科学计数法问题`;
      }
    }

    SpreadsheetApp.getUi().alert('账户ID格式检测结果', message, SpreadsheetApp.getUi().ButtonSet.OK);
  } catch (e) {
    handleError('检测账户ID格式失败', e);
  }
}
