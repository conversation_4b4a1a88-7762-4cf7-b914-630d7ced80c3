

/**
 * Google Sheets 数据导出和整合工具 - 简化版
 * 直接在Google环境中运行，生成单一汇总表格和昨日数据透视表
 *
 * 功能说明：
 * 1. 生成"近7日数据"工作表，包含所有源表格的汇总数据
 * 2. 在同一工作表右侧自动生成昨日数据的数据透视表
 * 3. 透视表按包名和使用人分组，显示消耗金额汇总
 *
 * 使用说明：
 * 1. 在Google Apps Script中创建新项目
 * 2. 将此代码复制到Code.gs文件中
 * 3. 运行main()函数开始数据导出
 * 4. 首次运行需要授权访问Google Sheets
 *
 * 权限要求：
 * - 您的Google账户必须对所有配置的源表格具有"查看者"权限或更高权限
 * - 脚本需要Google Sheets API的读取和写入权限
 * - 如果某些表格无法访问，脚本会跳过并在控制台显示错误信息
 *
 * 数据要求：
 * - 源表格必须包含"渠道"列（用于识别有效的数据表头行）
 * - 必须包含"日期"和"消耗"列（或类似命名的列）
 * - 支持的列名（不区分大小写）：
 *   * 日期相关：日期、date、时间
 *   * 费用相关：消耗、cost、花费、费用
 *   * 渠道相关：渠道、channel、媒体
 *   * 用户相关：使用人、user、用户
 *   * 包名相关：包名、package、应用
 *   * 广告系列：广告系列、campaign、系列名称
 *
 * 注意事项：
 * - 脚本会自动跳过隐藏的工作表
 * - 只处理最近7天的数据（可在CONFIG中修改FILTER_DAYS）
 * - Google Apps Script单次执行时间限制为6分钟
 * - 如果处理的表格过多可能超时，建议分批处理
 */

// ==================== 配置部分 ====================

// 表格配置列表 - 从当前Google Sheet的"配置"表中读取
// 如果"配置"表不存在或读取失败，则使用默认配置
let SHEET_CONFIGS = [];

/**
 * 从当前Google Sheet的"配置"表中读取表格配置
 * 配置表格式：第一列为表格名称，第二列为表格URL
 */
function loadSheetConfigsFromConfigSheet() {
  try {
    console.log('🔧 开始从"配置"表中读取表格配置...');

    // 获取当前活动的表格
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

    // 查找"配置"工作表
    const configSheet = spreadsheet.getSheetByName('配置');
    if (!configSheet) {
      console.log('⚠️ 未找到"配置"工作表，使用默认配置');
      return getDefaultSheetConfigs();
    }

    // 获取配置表的数据
    const range = configSheet.getDataRange();
    if (range.getNumRows() <= 1) {
      console.log('⚠️ "配置"工作表没有数据，使用默认配置');
      return getDefaultSheetConfigs();
    }

    const values = range.getValues();
    const configs = [];

    // 跳过表头行，从第二行开始读取
    for (let i = 1; i < values.length; i++) {
      const row = values[i];

      // 检查行是否有效（至少有两列且都不为空）
      if (row.length >= 2 && row[0] && row[1]) {
        const title = row[0].toString().trim();
        const url = row[1].toString().trim();

        // 验证URL格式
        if (url.includes('docs.google.com/spreadsheets/d/')) {
          configs.push({
            title: title,
            url: url
          });
          console.log(`  ✅ 已加载配置: ${title}`);
        } else {
          console.log(`  ⚠️ 跳过无效URL: ${title} - ${url}`);
        }
      }
    }

    if (configs.length === 0) {
      console.log('⚠️ "配置"工作表中没有有效的配置，使用默认配置');
      return getDefaultSheetConfigs();
    }

    console.log(`✅ 成功从"配置"表中加载了 ${configs.length} 个表格配置`);
    return configs;

  } catch (error) {
    console.error(`❌ 从"配置"表读取配置时发生错误: ${error.message}`);
    console.log('🔄 使用默认配置');
    return getDefaultSheetConfigs();
  }
}

/**
 * 获取默认的表格配置（原硬编码配置）
 */
function getDefaultSheetConfigs() {
  return [
    {
      title: 'NEXO-数据日报表',
      url: 'https://docs.google.com/spreadsheets/d/12d8JKdJQG62sftuzLWOyAkN_GIFyi3y-nvjcf8tidE0/edit?usp=sharing'
    },
    {
      title: 'Spin777-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1kXlOV3vapB00bqi8A8XBkzYJ6bAkP-m_Bhh8fA2FE80/edit?usp=sharing'
    },
    {
      title: 'Spin101-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1ZYHmVAVYc-3YWHq07JRPn2tvvVg5GF6_6xwB-8KDro8/edit?usp=sharing'
    },
    {
      title: 'Arcade-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1DBY8ynmwmybAKZu7hNEwWvnYqPD8lm7Kd26ETSSWfd4/edit?usp=sharing'
    },
    {
      title: 'LG-代投GG&FB&8+1',
      url: 'https://docs.google.com/spreadsheets/d/1qoC-R9WEt34gyfFycSyLd6Fl3rvgWCRtAJjPaInVr3A/edit?usp=sharing'
    },
    {
      title: '代投-赵总-重庆2（6+1）',
      url: 'https://docs.google.com/spreadsheets/d/1PYC3IodQqnZp0_8crRYV3qJjSpWua6xwXh5BjeP23EE/edit?usp=sharing'
    },
    {
      title: 'FQ-日报表',
      url: 'https://docs.google.com/spreadsheets/d/13uyMaE9fVsuMqluSSxarxA08uUBrZ-O0SZDm5F2sfWs/edit?usp=sharing'
    },
    {
      title: 'DC-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1McFMZrEuSlmgdoPJ56K-qf825mv-7jR0-5XnVNbaVFA/edit?usp=sharing'
    },
    {
      title: '桃子-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1E8nLC98WPMjtQ3Xmnx9UUUdWypUiGPikGZdzH3lTicU/edit?usp=sharing'
    },
    {
      title: 'zero-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1_DOnBZS9ULlxtR1Edpo4Wgdtvcfp9Z7JFzZcY98MUAk/edit?usp=sharing'
    },
    {
      title: '帆-日报表',
      url: 'https://docs.google.com/spreadsheets/d/11xc1cqMcFbkvFLYokHBBOm4pcSbJJTxTbIOxLeLWovI/edit?usp=sharing'
    },
    {
      title: '强-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1wiyJp8CepIS5pbh9f98U-k0Voushh8TYIDBOK7CFRcA/edit?usp=sharing'
    },
    {
      title: 'AA77-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1YN-ylXHBFjL6SMqOWQ0v0P71QAO5H1uNJrRHugKVmL0/edit?usp=sharing'
    },
    {
      title: '金-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1U6Cwdt2WMp6WSgABCXGgSj0iqHfMhImbB4jsF2QV-eY/edit?usp=sharing'
    },
    {
      title: '安利-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1tfnuZkzGpq4W2Hw2rG4raG4tEhxgonkB287SSrcf3IQ/edit?usp=sharing'
    },
    {
      title: '榴莲-日报表',
      url: 'https://docs.google.com/spreadsheets/d/1PQD5qYndcQhwwpFXkUL4AGcOpkRWSbMosBVM4Y0dKxQ/edit?usp=sharing'
    }
  ];
}

// 配置参数
const CONFIG = {
  FILTER_DAYS: 7,  // 筛选最近N天的数据
  OUTPUT_SHEET_NAME: '近7日数据',  // 固定的工作表名称
  DATE_FORMATS: [
    'yyyy-MM-dd', 'yyyy/MM/dd', 'MM/dd/yyyy', 'dd/MM/yyyy',
    'MM-dd', 'MM/dd'
  ]
};

// ==================== 主要函数 ====================

/**
 * 主入口函数 - 简化版
 */
function main() {
  console.log('🚀 开始执行Google Sheets数据导出任务...');
  const startTime = new Date();

  try {
    // 首先从"配置"表中加载表格配置
    SHEET_CONFIGS = loadSheetConfigsFromConfigSheet();
    console.log(`📋 已加载 ${SHEET_CONFIGS.length} 个表格配置`);

    // 创建输出表格
    const outputResult = createOutputSheet();
    const outputSheet = outputResult.sheet;
    const outputSpreadsheet = outputResult.spreadsheet;

    // 处理所有表格
    const allData = processAllSheets();

    // 筛选最近N天的数据
    const filteredData = filterRecentData(allData);
    console.log(`筛选前总行数: ${allData.length}, 筛选后总行数: ${filteredData.length}`);

    // 保存到输出表格
    if (filteredData.length > 0) {
      saveToOutputSheet(outputSheet, filteredData);
      console.log(`✅ 数据已保存到工作表: ${outputSheet.getName()}`);

      // 在右侧创建昨日数据透视表
      createYesterdayPivotTableInSameSheet(outputSheet, filteredData);

      const endTime = new Date();
      const duration = (endTime - startTime) / 1000;
      console.log(`🎉 任务完成！总耗时: ${duration.toFixed(2)} 秒`);
    } else {
      console.log('⚠️ 没有找到符合条件的数据');
    }

  } catch (error) {
    console.error('❌ 执行过程中发生错误:', error);
    throw error;
  }
}

/**
 * 处理所有表格
 */
function processAllSheets() {
  const allData = [];
  let successCount = 0;
  let failCount = 0;
  
  console.log(`📊 开始处理 ${SHEET_CONFIGS.length} 个表格...`);
  
  for (let i = 0; i < SHEET_CONFIGS.length; i++) {
    const config = SHEET_CONFIGS[i];
    console.log(`处理表格 ${i + 1}/${SHEET_CONFIGS.length}: ${config.title}`);
    
    try {
      const sheetData = processSingleSheet(config);
      if (sheetData.length > 0) {
        allData.push(...sheetData);
        successCount++;
        console.log(`  ✅ 成功处理，获得 ${sheetData.length} 行数据`);
      } else {
        console.log(`  ⚠️ 未获得有效数据`);
      }
    } catch (error) {
      failCount++;
      console.error(`  ❌ 处理失败: ${error.message}`);
    }
  }
  
  console.log(`📈 处理完成: 成功 ${successCount} 个，失败 ${failCount} 个，总数据行数: ${allData.length}`);
  return allData;
}

/**
 * 处理单个表格
 */
function processSingleSheet(config) {
  try {
    const spreadsheetId = extractSheetId(config.url);
    const spreadsheet = SpreadsheetApp.openById(spreadsheetId);
    const sheets = spreadsheet.getSheets();
    
    const allSheetData = [];
    
    for (const sheet of sheets) {
      // 跳过隐藏的工作表
      if (sheet.isSheetHidden()) {
        console.log(`  - 跳过隐藏工作表: ${sheet.getName()}`);
        continue;
      }
      
      const sheetData = extractSheetData(sheet, config.title);
      if (sheetData.length > 0) {
        allSheetData.push(...sheetData);
        console.log(`    - 工作表 "${sheet.getName()}" 获得 ${sheetData.length} 行数据`);
      }
    }
    
    return allSheetData;
    
  } catch (error) {
    console.error(`处理表格 "${config.title}" 时发生错误: ${error.message}`);
    return [];
  }
}

/**
 * 从工作表中提取数据
 */
function extractSheetData(sheet, sourceTableName) {
  try {
    const range = sheet.getDataRange();
    if (range.getNumRows() === 0) {
      return [];
    }

    const values = range.getValues();
    const headerRowIndex = findHeaderRow(values);

    if (headerRowIndex === -1) {
      console.log(`    - 工作表 "${sheet.getName()}" 未找到包含"渠道"的表头行`);
      return [];
    }

    const headers = values[headerRowIndex];
    const dataRows = values.slice(headerRowIndex + 1);

    // 查找关键列的索引
    const columnIndices = findColumnIndices(headers);

    // 如果没有找到日期或消耗列，跳过此工作表
    if (columnIndices.date === -1 || columnIndices.cost === -1) {
      console.log(`    - 工作表 "${sheet.getName()}" 缺少必需的列（日期或消耗）`);
      return [];
    }

    const extractedData = [];

    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];

      // 跳过空行
      if (row.every(cell => !cell || cell.toString().trim() === '')) {
        continue;
      }

      const dateValue = row[columnIndices.date];
      const costValue = row[columnIndices.cost];

      if (!dateValue || (!costValue && costValue !== 0)) {
        continue;
      }

      const parsedDate = parseDate(dateValue);
      if (!parsedDate) {
        continue;
      }

      // 改进数字解析
      let cost = 0;
      if (typeof costValue === 'number') {
        cost = costValue;
      } else {
        const costStr = costValue.toString().replace(/[^\d.-]/g, '');
        cost = parseFloat(costStr);
      }
      
      if (isNaN(cost)) {
        continue;
      }

      extractedData.push({
        date: Utilities.formatDate(parsedDate, Session.getScriptTimeZone(), 'yyyy-MM-dd'),
        dateObj: parsedDate,
        cost: cost,
        campaign: row[columnIndices.campaign] ? row[columnIndices.campaign].toString() : '',
        channel: row[columnIndices.channel] ? row[columnIndices.channel].toString() : '',
        user: row[columnIndices.user] ? row[columnIndices.user].toString() : '',
        package: row[columnIndices.package] ? row[columnIndices.package].toString() : '',
        sheetTitle: sheet.getName(),
        sourceTable: sourceTableName
      });
    }

    return extractedData;

  } catch (error) {
    console.error(`提取工作表 "${sheet.getName()}" 数据时发生错误: ${error.message}`);
    return [];
  }
}

// ==================== 辅助函数 ====================

/**
 * 从URL中提取表格ID
 */
function extractSheetId(url) {
  const match = url.match(/\/d\/([a-zA-Z0-9-_]+)/);
  if (match) {
    return match[1];
  }
  throw new Error(`无法从URL中提取有效的Sheet ID: ${url}`);
}

/**
 * 查找包含"渠道"的表头行
 */
function findHeaderRow(values) {
  for (let i = 0; i < Math.min(values.length, 10); i++) {
    const row = values[i];
    for (const cell of row) {
      if (cell && cell.toString().includes('渠道')) {
        return i;
      }
    }
  }
  return -1;
}

/**
 * 查找关键列的索引
 */
function findColumnIndices(headers) {
  const indices = {
    date: -1,
    cost: -1,
    campaign: -1,
    channel: -1,
    user: -1,
    package: -1
  };

  for (let i = 0; i < headers.length; i++) {
    const header = headers[i] ? headers[i].toString().toLowerCase().trim() : '';

    if (header.includes('日期') || header.includes('date') || header === '时间') {
      indices.date = i;
    } else if (header.includes('消耗') || header.includes('cost') || header.includes('花费') || header.includes('费用')) {
      indices.cost = i;
    } else if (header.includes('广告系列') || header.includes('campaign') || header.includes('系列名称')) {
      indices.campaign = i;
    } else if (header.includes('渠道') || header.includes('channel') || header.includes('媒体')) {
      indices.channel = i;
    } else if (header.includes('使用人') || header.includes('user') || header.includes('用户')) {
      indices.user = i;
    } else if (header.includes('包名') || header.includes('package') || header.includes('应用')) {
      indices.package = i;
    }
  }

  // 验证必需的列是否找到
  if (indices.date === -1) {
    console.warn('⚠️ 未找到日期列');
  }
  if (indices.cost === -1) {
    console.warn('⚠️ 未找到消耗列');
  }

  return indices;
}

/**
 * 解析日期
 */
function parseDate(dateValue) {
  if (!dateValue) return null;

  // 如果已经是Date对象
  if (dateValue instanceof Date) {
    return dateValue;
  }

  const dateStr = dateValue.toString().trim();
  if (!dateStr) return null;

  // 尝试直接解析
  let date = new Date(dateStr);
  if (!isNaN(date.getTime())) {
    return date;
  }

  // 尝试各种格式
  const formats = [
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/,  // YYYY-MM-DD
    /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,  // YYYY/MM/DD
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,  // MM/DD/YYYY
    /^(\d{1,2})-(\d{1,2})$/,  // MM-DD
    /^(\d{1,2})\/(\d{1,2})$/   // MM/DD
  ];

  for (const format of formats) {
    const match = dateStr.match(format);
    if (match) {
      let year, month, day;

      if (match.length === 4) {  // 包含年份
        if (format.source.startsWith('^(\\d{4})')) {  // YYYY-MM-DD 或 YYYY/MM/DD
          year = parseInt(match[1]);
          month = parseInt(match[2]) - 1;
          day = parseInt(match[3]);
        } else {  // MM/DD/YYYY
          month = parseInt(match[1]) - 1;
          day = parseInt(match[2]);
          year = parseInt(match[3]);
        }
      } else {  // 不包含年份，使用当前年份
        const currentYear = new Date().getFullYear();
        year = currentYear;
        month = parseInt(match[1]) - 1;
        day = parseInt(match[2]);
      }

      date = new Date(year, month, day);
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
  }

  return null;
}

/**
 * 筛选最近N天的数据
 */
function filterRecentData(data) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - CONFIG.FILTER_DAYS);

  return data.filter(row => {
    return row.dateObj && row.dateObj >= cutoffDate;
  });
}

/**
 * 创建输出表格 - 在当前表格中创建新工作表
 */
function createOutputSheet() {
  // 获取当前活动的表格，如果没有则创建新表格
  let spreadsheet;
  try {
    spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  } catch (error) {
    // 如果没有活动表格，创建新的表格
    spreadsheet = SpreadsheetApp.create('广告数据汇总表');
    console.log(`📊 已创建新表格: ${spreadsheet.getName()}`);
  }
  
  const sheetName = CONFIG.OUTPUT_SHEET_NAME;

  // 检查是否已存在同名工作表，如果存在则删除
  const existingSheet = spreadsheet.getSheetByName(sheetName);
  if (existingSheet) {
    // 确保不是最后一个工作表
    if (spreadsheet.getSheets().length > 1) {
      spreadsheet.deleteSheet(existingSheet);
      console.log(`🗑️ 已删除现有工作表: ${sheetName}`);
    } else {
      // 如果是最后一个工作表，先创建新的再删除旧的
      const tempSheet = spreadsheet.insertSheet('temp');
      spreadsheet.deleteSheet(existingSheet);
      spreadsheet.deleteSheet(tempSheet);
    }
  }

  // 创建新工作表
  const sheet = spreadsheet.insertSheet(sheetName);

  // 设置表头
  const headers = ['日期', '来源表格', '工作表', '渠道', '包名', '消耗($)', '使用人'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);

  // 格式化表头
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#4285f4');
  headerRange.setFontColor('white');

  console.log(`📊 已创建工作表: ${sheetName}`);

  return {
    sheet: sheet,
    spreadsheet: spreadsheet
  };
}

/**
 * 保存数据到输出表格
 */
function saveToOutputSheet(sheet, data) {
  if (data.length === 0) return;

  // 按日期排序
  data.sort((a, b) => new Date(b.date) - new Date(a.date));

  // 准备数据行
  const rows = data.map(row => [
    row.date,
    row.sourceTable,
    row.sheetTitle,
    row.channel,
    row.package,
    row.cost,
    row.user
  ]);

  // 写入数据
  const startRow = 2;  // 从第2行开始（第1行是表头）
  sheet.getRange(startRow, 1, rows.length, 7).setValues(rows);

  // 格式化数据
  formatOutputSheet(sheet, rows.length);

  console.log(`💾 已保存 ${data.length} 行数据到输出表格`);
}

/**
 * 格式化输出表格
 */
function formatOutputSheet(sheet, dataRows) {
  // 自动调整列宽
  sheet.autoResizeColumns(1, 7);

  // 格式化消耗列为货币格式（现在是第6列）
  const costRange = sheet.getRange(2, 6, dataRows, 1);
  costRange.setNumberFormat('$#,##0.00');

  // 添加边框
  const dataRange = sheet.getRange(1, 1, dataRows + 1, 7);
  dataRange.setBorder(true, true, true, true, true, true);

  // 冻结表头行
  sheet.setFrozenRows(1);
}

/**
 * 设置定时触发器（可选）
 * 每天指定时间自动运行
 */
function createDailyTrigger(hour = 14) {
  ScriptApp.newTrigger('main')
    .timeBased()
    .everyDays(1)
    .atHour(hour)
    .create();

  console.log(`✅ 已创建每日定时触发器（每天${hour}点运行）`);
}







/**
 * 删除所有触发器
 */
function deleteAllTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => ScriptApp.deleteTrigger(trigger));
  console.log(`🗑️ 已删除 ${triggers.length} 个触发器`);
}

// ==================== 菜单栏功能 ====================

/**
 * 当表格打开时自动创建自定义菜单
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();

  ui.createMenu('📊 日报导出工具')
    .addItem('🚀 执行数据导出', 'main')
    .addSeparator()
    .addSubMenu(ui.createMenu('⚙️ 高级功能')
      .addItem('🔄 设置每日定时任务', 'createDailyTriggerWithTimeSelection')
      .addItem('� 查看定时任务状态', 'showTriggerStatus')
      .addItem('�🗑️ 删除所有定时任务', 'deleteAllTriggersWithConfirm')
      .addItem('📋 查看配置信息', 'showConfigInfo'))
    .addSeparator()
    .addSubMenu(ui.createMenu('🔧 配置管理')
      .addItem('📝 创建配置表模板', 'createConfigSheetTemplate')
      .addItem('🔄 重新加载配置', 'reloadSheetConfigs'))
    .addSeparator()
    .addItem('❓ 使用帮助', 'showHelp')
    .addToUi();
}

/**
 * 显示配置信息
 */
function showConfigInfo() {
  const ui = SpreadsheetApp.getUi();

  // 重新加载配置以确保显示最新信息
  const currentConfigs = loadSheetConfigsFromConfigSheet();

  let configText = '📊 当前配置信息：\n\n';
  configText += `📅 数据筛选天数：${CONFIG.FILTER_DAYS} 天\n`;
  configText += `📋 输出工作表名称：${CONFIG.OUTPUT_SHEET_NAME}\n`;
  configText += `📊 配置的源表格数量：${currentConfigs.length} 个\n\n`;

  // 检查配置来源
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = spreadsheet.getSheetByName('配置');
  if (configSheet) {
    configText += '📋 配置来源：当前工作簿的"配置"表\n\n';
  } else {
    configText += '📋 配置来源：默认硬编码配置（未找到"配置"表）\n\n';
  }

  configText += '📋 源表格列表：\n';

  currentConfigs.forEach((config, index) => {
    configText += `${index + 1}. ${config.title}\n`;
  });

  ui.alert('配置信息', configText, ui.ButtonSet.OK);
}

/**
 * 显示定时任务状态
 */
function showTriggerStatus() {
  const ui = SpreadsheetApp.getUi();
  const triggers = ScriptApp.getProjectTriggers();

  let statusText = '📅 定时任务状态：\n\n';

  if (triggers.length === 0) {
    statusText += '❌ 当前没有设置任何定时任务\n\n';
    statusText += '💡 提示：可以通过"设置每日定时任务"来创建自动执行任务';
  } else {
    statusText += `✅ 当前共有 ${triggers.length} 个定时任务：\n\n`;

    triggers.forEach((trigger, index) => {
      const handlerFunction = trigger.getHandlerFunction();
      const triggerSource = trigger.getTriggerSource();

      if (triggerSource === ScriptApp.TriggerSource.CLOCK) {
        const eventType = trigger.getEventType();
        if (eventType === ScriptApp.EventType.CLOCK) {
          // 获取触发器的详细信息
          const triggerInfo = trigger.toString();

          // 尝试从触发器信息中提取时间（这是一个近似方法）
          statusText += `${index + 1}. 函数：${handlerFunction}\n`;
          statusText += `   类型：每日定时任务\n`;
          statusText += `   状态：已激活\n`;

          // 由于Google Apps Script API限制，无法直接获取具体时间
          // 但我们可以显示触发器的基本信息
          statusText += `   详情：${triggerInfo}\n\n`;
        }
      }
    });

    statusText += '💡 提示：如需修改时间，请先删除现有任务再重新设置';
  }

  ui.alert('定时任务状态', statusText, ui.ButtonSet.OK);
}

/**
 * 显示使用帮助
 */
function showHelp() {
  const ui = SpreadsheetApp.getUi();

  const helpText = `📖 广告数据导出工具使用说明（简化版）

🚀 主要功能：
• 执行数据导出：将所有配置的表格数据汇总到"近7日数据"工作表
• 自动生成昨日数据透视表：在同一工作表右侧显示昨日数据的汇总分析

📊 透视表说明：
• 按包名和使用人分组显示昨日消耗数据
• 自动计算各维度的消耗金额汇总
• 支持货币格式显示和美观的表格样式

⚙️ 高级功能：
• 设置每日定时任务：可自定义每天几点自动执行数据导出（0-23小时）
• 删除所有定时任务：清除所有自动执行任务
• 查看配置信息：显示当前配置的表格和参数

� 配置管理：
• 创建配置表模板：在当前工作簿中创建"配置"工作表，用于管理源表格列表
• 重新加载配置：从"配置"表中重新读取表格配置信息
• 配置表格式：第一列为表格名称，第二列为完整的Google Sheets URL

�📋 数据要求：
• 源表格必须包含"渠道"列（用于识别表头）
• 必须包含"日期"和"消耗"列
• 支持多种列名格式（中英文）

⚠️ 注意事项：
• 首次运行需要授权访问Google Sheets
• 脚本会自动跳过无权限访问的表格
• 单次执行时间限制为6分钟
• 只处理最近${CONFIG.FILTER_DAYS}天的数据
• 如果没有"配置"表，将使用默认的硬编码配置

💡 配置管理提示：
• 使用"创建配置表模板"可以快速创建配置表
• 修改配置后记得使用"重新加载配置"来应用更改
• 配置表支持添加、删除、修改表格信息

如有问题，请检查控制台日志获取详细信息。`;

  ui.alert('使用帮助', helpText, ui.ButtonSet.OK);
}



/**
 * 创建定时触发器的包装函数（带时间选择）
 */
function createDailyTriggerWithTimeSelection() {
  const ui = SpreadsheetApp.getUi();

  // 先检查是否已有触发器
  const existingTriggers = ScriptApp.getProjectTriggers();
  if (existingTriggers.length > 0) {
    const response = ui.alert(
      '已有定时任务',
      '检测到已存在定时任务，是否要删除现有任务并重新设置？',
      ui.ButtonSet.YES_NO
    );

    if (response === ui.Button.YES) {
      // 删除现有触发器
      existingTriggers.forEach(trigger => ScriptApp.deleteTrigger(trigger));
      console.log(`🗑️ 已删除 ${existingTriggers.length} 个现有触发器`);
    } else {
      return; // 用户选择不继续
    }
  }

  // 让用户输入时间
  const timeInput = ui.prompt(
    '设置定时任务',
    '请输入每日执行时间（24小时制，0-23）：\n例如：9（上午9点）、14（下午2点）、18（下午6点）',
    ui.ButtonSet.OK_CANCEL
  );

  if (timeInput.getSelectedButton() === ui.Button.OK) {
    const hourText = timeInput.getResponseText().trim();
    const hour = parseInt(hourText);

    // 验证输入
    if (isNaN(hour) || hour < 0 || hour > 23) {
      ui.alert('输入错误', '请输入0-23之间的整数（24小时制）', ui.ButtonSet.OK);
      return;
    }

    try {
      createDailyTrigger(hour);
      const timeDisplay = hour === 0 ? '午夜12点' :
                         hour < 12 ? `上午${hour}点` :
                         hour === 12 ? '中午12点' :
                         `下午${hour}点`;
      ui.alert('设置成功', `已成功设置每日定时任务（每天${timeDisplay}执行）`, ui.ButtonSet.OK);
    } catch (error) {
      ui.alert('设置失败', `设置定时任务时发生错误：${error.message}`, ui.ButtonSet.OK);
    }
  }
}

/**
 * 删除所有触发器的包装函数（带用户确认）
 */
function deleteAllTriggersWithConfirm() {
  const ui = SpreadsheetApp.getUi();

  const response = ui.alert(
    '删除定时任务',
    '此操作将删除所有定时任务，是否继续？',
    ui.ButtonSet.YES_NO
  );

  if (response === ui.Button.YES) {
    try {
      deleteAllTriggers();
      ui.alert('删除成功', '已成功删除所有定时任务', ui.ButtonSet.OK);
    } catch (error) {
      ui.alert('删除失败', `删除定时任务时发生错误：${error.message}`, ui.ButtonSet.OK);
    }
  }
}

// ==================== 数据透视表功能 ====================

/**
 * 测试函数 - 创建示例数据透视表
 */
function testPivotTable() {
  try {
    console.log('🧪 开始测试数据透视表功能...');

    // 获取当前活动的工作表
    const sheet = SpreadsheetApp.getActiveSheet();

    // 创建测试数据
    const testData = [
      { date: '2024-01-27', package: '包名A', user: '用户1', cost: 100 },
      { date: '2024-01-27', package: '包名A', user: '用户2', cost: 150 },
      { date: '2024-01-27', package: '包名B', user: '用户1', cost: 200 },
      { date: '2024-01-27', package: '包名B', user: '用户3', cost: 80 },
      { date: '2024-01-27', package: '包名C', user: '用户2', cost: 120 }
    ];

    // 清空J列开始的区域
    sheet.getRange('J1:Z50').clear();

    // 设置标题
    sheet.getRange('J1').setValue('测试数据透视表');
    sheet.getRange('J1').setFontWeight('bold').setFontSize(12).setFontColor('#1a73e8');

    // 创建手动数据透视表
    const success = createManualPivotTable(sheet, testData, 10);

    if (success) {
      console.log('✅ 测试成功！数据透视表已创建');
    } else {
      console.log('❌ 测试失败！');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

/**
 * 创建手动数据透视表（作为备选方案）
 */
function createManualPivotTable(sheet, yesterdayData, startCol) {
  try {
    console.log('📊 开始创建手动数据透视表...');

    // 按包名和使用人分组汇总数据
    const pivotData = {};

    yesterdayData.forEach(row => {
      const packageName = row.package || '未知包名';
      const userName = row.user || '未知用户';
      const cost = parseFloat(row.cost) || 0;

      if (!pivotData[packageName]) {
        pivotData[packageName] = {};
      }

      if (!pivotData[packageName][userName]) {
        pivotData[packageName][userName] = 0;
      }

      pivotData[packageName][userName] += cost;
    });

    // 获取所有唯一的用户名
    const allUsers = new Set();
    Object.values(pivotData).forEach(packageData => {
      Object.keys(packageData).forEach(user => allUsers.add(user));
    });
    const userList = Array.from(allUsers).sort();

    // 创建表头
    const headers = ['包名', ...userList, '总计'];
    sheet.getRange(3, startCol, 1, headers.length).setValues([headers]);

    // 设置表头样式
    const headerRange = sheet.getRange(3, startCol, 1, headers.length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('white');

    // 填充数据
    const packages = Object.keys(pivotData).sort();
    const dataRows = [];

    packages.forEach(packageName => {
      const row = [packageName];
      let rowTotal = 0;

      userList.forEach(user => {
        const value = pivotData[packageName][user] || 0;
        row.push(value);
        rowTotal += value;
      });

      row.push(rowTotal);
      dataRows.push(row);
    });

    // 添加总计行
    const totalRow = ['总计'];
    let grandTotal = 0;

    userList.forEach(user => {
      let userTotal = 0;
      packages.forEach(packageName => {
        userTotal += pivotData[packageName][user] || 0;
      });
      totalRow.push(userTotal);
      grandTotal += userTotal;
    });
    totalRow.push(grandTotal);
    dataRows.push(totalRow);

    // 写入数据
    if (dataRows.length > 0) {
      sheet.getRange(4, startCol, dataRows.length, headers.length).setValues(dataRows);

      // 格式化数据
      const dataRange = sheet.getRange(4, startCol, dataRows.length, headers.length);
      dataRange.setBorder(true, true, true, true, true, true);

      // 格式化数值列为货币格式（除了第一列包名）
      for (let i = 1; i < headers.length; i++) {
        const colRange = sheet.getRange(4, startCol + i, dataRows.length, 1);
        colRange.setNumberFormat('$#,##0.00');
      }

      // 设置总计行样式
      const totalRowRange = sheet.getRange(3 + dataRows.length, startCol, 1, headers.length);
      totalRowRange.setFontWeight('bold');
      totalRowRange.setBackground('#f8f9fa');

      // 自动调整列宽
      sheet.autoResizeColumns(startCol, headers.length);
    }

    console.log(`✅ 手动数据透视表创建完成，包含 ${packages.length} 个包名，${userList.length} 个用户`);
    return true;

  } catch (error) {
    console.error(`❌ 创建手动数据透视表时发生错误: ${error.message}`);
    return false;
  }
}

/**
 * 在同一工作表右侧创建昨日数据透视表
 */
function createYesterdayPivotTableInSameSheet(sheet, allData) {
  try {
    // 获取昨日日期
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = Utilities.formatDate(yesterday, Session.getScriptTimeZone(), 'yyyy-MM-dd');

    // 筛选昨日数据
    const yesterdayData = allData.filter(row => row.date === yesterdayStr);

    if (yesterdayData.length === 0) {
      console.log('⚠️ 没有找到昨日数据，跳过创建数据透视表');
      // 在J1显示提示信息
      sheet.getRange('J1').setValue(`昨日数据透视表 (${yesterdayStr}) - 无数据`);
      sheet.getRange('J1').setFontWeight('bold').setFontSize(12).setFontColor('#ff6d01');
      return;
    }

    console.log(`📊 开始创建昨日数据透视表，数据行数: ${yesterdayData.length}`);

    // 在第J列（第10列）开始创建透视表标题
    sheet.getRange('J1').setValue(`昨日数据透视表 (${yesterdayStr})`);
    sheet.getRange('J1').setFontWeight('bold').setFontSize(12).setFontColor('#1a73e8');

    // 尝试创建手动数据透视表（更可靠的方法）
    const manualSuccess = createManualPivotTable(sheet, yesterdayData, 10);

    if (manualSuccess) {
      console.log(`✅ 已在J列创建昨日数据透视表（手动方式）`);
      return;
    }

    // 如果手动方式失败，尝试原生透视表
    console.log('🔄 手动透视表创建失败，尝试原生透视表...');

    // 准备昨日数据源（从第J3开始，为原生透视表准备）
    const headers = ['日期', '来源表格', '工作表', '渠道', '包名', '消耗($)', '使用人'];
    const dataRows = yesterdayData.map(row => [
      row.date,
      row.sourceTable,
      row.sheetTitle,
      row.channel,
      row.package,
      row.cost,
      row.user
    ]);

    const sourceData = [headers, ...dataRows];

    // 将数据源放在R列，避免与手动透视表冲突
    const sourceRange = sheet.getRange(3, 18, sourceData.length, headers.length);
    sourceRange.setValues(sourceData);

    console.log(`数据源范围: R3:${String.fromCharCode(82 + headers.length - 1)}${3 + sourceData.length - 1}`);

    // 在右侧创建原生数据透视表（从R列开始）
    const pivotTable = sheet.getRange(3, 18).createPivotTable(sourceRange);

    // 配置透视表
    const packageDimension = pivotTable.addRowGroup(5); // 包名是第5列
    packageDimension.showTotals(true);
    packageDimension.sortAscending();

    const userDimension = pivotTable.addColumnGroup(7); // 使用人是第7列
    userDimension.showTotals(true);
    userDimension.sortAscending();

    const costValue = pivotTable.addPivotValue(6, SpreadsheetApp.PivotTableSummarizeFunction.SUM);
    costValue.setDisplayName('消耗($)');

    // 隐藏数据源列（R到X列）
    sheet.hideColumns(18, 7);

    // 等待透视表创建完成
    Utilities.sleep(2000);

    console.log(`✅ 已创建原生数据透视表作为备选`);

  } catch (error) {
    console.error(`❌ 创建数据透视表时发生错误: ${error.message}`);
    console.error(`错误详情: ${error.stack}`);

    // 在出错时显示错误信息
    sheet.getRange('J2').setValue(`透视表创建失败: ${error.message}`);
    sheet.getRange('J2').setFontWeight('bold').setFontSize(10).setFontColor('#d93025');
  }
}

/**
 * 格式化同一工作表中的数据透视表
 */
function formatPivotTableInSameSheet(sheet) {
  try {
    // 等待透视表创建完成
    Utilities.sleep(1000);

    // 获取工作表的实际数据范围
    const lastRow = sheet.getLastRow();
    const lastCol = sheet.getLastColumn();

    console.log(`工作表最后行: ${lastRow}, 最后列: ${lastCol}`);

    // 透视表从J列（第10列）开始
    const pivotStartCol = 10;

    if (lastRow > 3 && lastCol >= pivotStartCol) {
      // 自动调整透视表列宽（从J列开始）
      const colsToResize = lastCol - pivotStartCol + 1;
      sheet.autoResizeColumns(pivotStartCol, colsToResize);

      // 计算透视表的实际范围
      const pivotStartRow = 3;
      const pivotRows = lastRow - pivotStartRow + 1;
      const pivotCols = lastCol - pivotStartCol + 1;

      if (pivotRows > 0 && pivotCols > 0) {
        console.log(`透视表范围: ${pivotStartRow}行到${lastRow}行, ${pivotStartCol}列到${lastCol}列`);

        // 添加边框到整个透视表区域
        const pivotRange = sheet.getRange(pivotStartRow, pivotStartCol, pivotRows, pivotCols);
        pivotRange.setBorder(true, true, true, true, true, true);

        // 设置表头样式（前两行）
        if (pivotRows >= 2) {
          const headerRange = sheet.getRange(pivotStartRow, pivotStartCol, 2, pivotCols);
          headerRange.setFontWeight('bold');
          headerRange.setBackground('#e8f0fe');
        }

        // 格式化数值为货币格式（尝试整个透视表区域）
        try {
          // 先尝试格式化整个透视表区域的数值
          const valueRange = sheet.getRange(pivotStartRow, pivotStartCol, pivotRows, pivotCols);

          // 获取所有值并检查哪些是数字
          const values = valueRange.getValues();
          for (let i = 0; i < values.length; i++) {
            for (let j = 0; j < values[i].length; j++) {
              const cellValue = values[i][j];
              if (typeof cellValue === 'number' && cellValue > 0) {
                // 对包含数字的单元格设置货币格式
                const cellRange = sheet.getRange(pivotStartRow + i, pivotStartCol + j);
                cellRange.setNumberFormat('$#,##0.00');
              }
            }
          }
        } catch (formatError) {
          console.warn(`格式化数值时发生错误: ${formatError.message}`);
        }
      }
    } else {
      console.warn(`透视表范围无效: lastRow=${lastRow}, lastCol=${lastCol}, pivotStartCol=${pivotStartCol}`);
    }

  } catch (error) {
    console.error(`格式化透视表时发生错误: ${error.message}`);
    console.error(`错误详情: ${error.stack}`);
  }
}

// ==================== 配置管理功能 ====================

/**
 * 创建配置表模板
 */
function createConfigSheetTemplate() {
  try {
    const ui = SpreadsheetApp.getUi();
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

    // 检查是否已存在配置表
    const existingConfigSheet = spreadsheet.getSheetByName('配置');
    if (existingConfigSheet) {
      const response = ui.alert(
        '配置表已存在',
        '检测到已存在"配置"工作表，是否要重新创建？\n（注意：这将删除现有配置数据）',
        ui.ButtonSet.YES_NO
      );

      if (response === ui.Button.YES) {
        spreadsheet.deleteSheet(existingConfigSheet);
        console.log('🗑️ 已删除现有配置表');
      } else {
        return;
      }
    }

    // 创建新的配置表
    const configSheet = spreadsheet.insertSheet('配置');

    // 设置表头
    const headers = ['表格名称', '表格URL', '备注'];
    configSheet.getRange(1, 1, 1, headers.length).setValues([headers]);

    // 格式化表头
    const headerRange = configSheet.getRange(1, 1, 1, headers.length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('white');

    // 添加默认配置数据作为示例
    const defaultConfigs = getDefaultSheetConfigs();
    const configData = defaultConfigs.map(config => [
      config.title,
      config.url,
      '默认配置'
    ]);

    if (configData.length > 0) {
      configSheet.getRange(2, 1, configData.length, 3).setValues(configData);
    }

    // 自动调整列宽
    configSheet.autoResizeColumns(1, 3);

    // 冻结表头行
    configSheet.setFrozenRows(1);

    // 添加数据验证说明
    configSheet.getRange('D1').setValue('使用说明：');
    configSheet.getRange('D2').setValue('1. 第一列填写表格名称');
    configSheet.getRange('D3').setValue('2. 第二列填写完整的Google Sheets URL');
    configSheet.getRange('D4').setValue('3. 第三列可填写备注信息（可选）');
    configSheet.getRange('D5').setValue('4. 修改配置后，请使用"重新加载配置"功能');

    // 格式化说明文字
    const instructionRange = configSheet.getRange('D1:D5');
    instructionRange.setFontSize(10);
    instructionRange.setFontColor('#666666');

    console.log('✅ 配置表模板创建完成');
    ui.alert('创建成功', `已成功创建"配置"工作表模板，包含 ${defaultConfigs.length} 个默认配置项`, ui.ButtonSet.OK);

  } catch (error) {
    console.error(`❌ 创建配置表模板时发生错误: ${error.message}`);
    SpreadsheetApp.getUi().alert('创建失败', `创建配置表模板时发生错误：${error.message}`, SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

/**
 * 重新加载配置
 */
function reloadSheetConfigs() {
  try {
    const ui = SpreadsheetApp.getUi();

    // 重新加载配置
    const newConfigs = loadSheetConfigsFromConfigSheet();
    SHEET_CONFIGS = newConfigs;

    console.log(`🔄 已重新加载 ${newConfigs.length} 个表格配置`);

    // 显示加载结果
    let message = `成功重新加载配置！\n\n`;
    message += `📊 配置数量：${newConfigs.length} 个\n\n`;

    // 检查配置来源
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const configSheet = spreadsheet.getSheetByName('配置');
    if (configSheet) {
      message += '📋 配置来源：当前工作簿的"配置"表\n\n';
    } else {
      message += '📋 配置来源：默认硬编码配置（未找到"配置"表）\n\n';
    }

    message += '前5个配置项：\n';
    newConfigs.slice(0, 5).forEach((config, index) => {
      message += `${index + 1}. ${config.title}\n`;
    });

    if (newConfigs.length > 5) {
      message += `... 还有 ${newConfigs.length - 5} 个配置项`;
    }

    ui.alert('重新加载完成', message, ui.ButtonSet.OK);

  } catch (error) {
    console.error(`❌ 重新加载配置时发生错误: ${error.message}`);
    SpreadsheetApp.getUi().alert('重新加载失败', `重新加载配置时发生错误：${error.message}`, SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

/**
 * 测试配置加载功能（调试用）
 */
function testConfigLoading() {
  console.log('🧪 开始测试配置加载功能...');

  try {
    // 测试加载配置
    const configs = loadSheetConfigsFromConfigSheet();
    console.log(`✅ 成功加载 ${configs.length} 个配置`);

    // 显示前3个配置
    configs.slice(0, 3).forEach((config, index) => {
      console.log(`${index + 1}. ${config.title} - ${config.url}`);
    });

    // 检查配置表是否存在
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const configSheet = spreadsheet.getSheetByName('配置');
    if (configSheet) {
      console.log('✅ 找到"配置"工作表');
      const range = configSheet.getDataRange();
      console.log(`配置表数据范围: ${range.getNumRows()} 行 x ${range.getNumColumns()} 列`);
    } else {
      console.log('⚠️ 未找到"配置"工作表，使用默认配置');
    }

    console.log('🎉 配置加载测试完成');

  } catch (error) {
    console.error(`❌ 测试过程中发生错误: ${error.message}`);
  }
}

