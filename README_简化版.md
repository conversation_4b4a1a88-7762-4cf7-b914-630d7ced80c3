# Google Sheets 数据导出工具 - 简化版

## 功能概述

这是一个简化版的Google Apps Script工具，专门用于：
1. **生成单一汇总表格**：创建"近7日数据"工作表，包含所有源表格的数据
2. **自动生成昨日数据透视表**：在同一工作表右侧显示昨日数据的分析

## 主要特性

### 📊 数据汇总
- 从16个配置的源表格中提取数据
- 自动筛选最近7天的数据
- 统一格式显示在"近7日数据"工作表中

### 📈 昨日数据透视表
- **位置**：在"近7日数据"工作表右侧（从R列开始）
- **分组方式**：
  - 行：按包名分组
  - 列：按使用人分组
  - 值：消耗金额汇总
- **格式化**：
  - 货币格式显示（$#,##0.00）
  - 专业的表格边框和样式
  - 表头加粗和背景色

### 🎯 简化优势
- **单一输出**：只生成一个工作表，避免混乱
- **自动分析**：昨日数据透视表自动生成，无需手动操作
- **布局优化**：数据源隐藏，只显示清晰的汇总表和透视表

## 使用方法

### 1. 设置脚本
1. 打开 [Google Apps Script](https://script.google.com/)
2. 创建新项目
3. 将 `日报提取.gs` 的内容复制到 `Code.gs` 文件中
4. 保存项目

### 2. 运行脚本
1. 点击运行按钮或执行 `main()` 函数
2. 首次运行需要授权访问Google Sheets
3. 等待脚本执行完成

### 3. 查看结果
- **主数据**：在"近7日数据"工作表的A-G列查看汇总数据
- **昨日透视表**：在同一工作表的R列开始查看昨日数据分析

## 工作表布局

```
近7日数据工作表布局：
A-G列：主数据表格
├── A: 日期
├── B: 来源表格  
├── C: 工作表
├── D: 渠道
├── E: 包名
├── F: 消耗($)
└── G: 使用人

J-P列：昨日数据源（隐藏）
R列开始：昨日数据透视表
├── 行分组：包名
├── 列分组：使用人
└── 数值：消耗金额汇总
```

## 配置说明

### 数据筛选
- **时间范围**：最近7天（可在CONFIG.FILTER_DAYS中修改）
- **数据源**：16个预配置的Google Sheets表格

### 透视表配置
- **昨日定义**：脚本运行日期的前一天
- **分组逻辑**：包名 × 使用人的交叉分析
- **数值计算**：消耗金额的求和

## 菜单功能

脚本会在Google Sheets中创建自定义菜单"📊 日报导出工具"：

- **🚀 执行数据导出**：运行主要功能
- **⚙️ 高级功能**：
  - 设置每日定时任务
  - 查看定时任务状态  
  - 删除所有定时任务
  - 查看配置信息
- **❓ 使用帮助**：显示详细说明

## 注意事项

### 权限要求
- 对所有源表格具有"查看者"权限或更高
- Google Sheets API的读取和写入权限

### 性能限制
- Google Apps Script单次执行时间限制：6分钟
- 如果源表格过多可能超时，建议分批处理

### 数据要求
- 源表格必须包含"渠道"列（用于识别表头行）
- 必须包含"日期"和"消耗"列
- 支持多种列名格式（中英文）

## 故障排除

### 常见问题
1. **没有昨日数据透视表**：检查是否有昨日的数据记录
2. **权限错误**：确保对所有源表格有访问权限
3. **执行超时**：源表格数据量过大，考虑减少数据范围

### 调试方法
- 查看Google Apps Script的执行日志
- 检查控制台输出的详细信息
- 验证源表格的数据格式

## 更新日志

### 简化版特性
- ✅ 移除多工作表生成功能
- ✅ 专注于单一"近7日数据"工作表
- ✅ 集成昨日数据透视表到同一工作表
- ✅ 优化布局和用户体验
- ✅ 简化配置和维护复杂度
