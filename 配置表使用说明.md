# 日报提取工具 - 配置表使用说明

## 概述

日报提取工具现在支持从当前Google Sheet的"配置"表中动态读取表格配置，无需修改代码即可管理源表格列表。

## 功能特点

- ✅ 从"配置"工作表动态读取表格配置
- ✅ 支持在线添加、删除、修改表格配置
- ✅ 自动创建配置表模板
- ✅ 配置验证和错误处理
- ✅ 向下兼容：如果没有配置表，自动使用默认配置

## 使用步骤

### 1. 创建配置表

1. 打开包含日报提取脚本的Google Sheet
2. 在菜单栏选择：**📊 日报导出工具** → **🔧 配置管理** → **📝 创建配置表模板**
3. 系统会自动创建"配置"工作表，并填入默认配置数据

### 2. 配置表格式

配置表包含以下列：

| 列名 | 说明 | 必填 |
|------|------|------|
| 表格名称 | 源表格的显示名称 | ✅ |
| 表格URL | 完整的Google Sheets URL | ✅ |
| 备注 | 可选的备注信息 | ❌ |

### 3. 配置表示例

```
表格名称                | 表格URL                                                           | 备注
NEXO-数据日报表        | https://docs.google.com/spreadsheets/d/12d8JKdJQG62sftuzL...    | 默认配置
Spin777-日报表         | https://docs.google.com/spreadsheets/d/1kXlOV3vapB00bqi8A...    | 默认配置
自定义表格             | https://docs.google.com/spreadsheets/d/1ABC123...               | 新增表格
```

### 4. 修改配置

1. 直接在"配置"工作表中编辑表格信息
2. 添加新行来增加表格
3. 删除行来移除表格
4. 修改URL或名称来更新配置

### 5. 应用配置更改

修改配置后，需要重新加载配置：
1. 在菜单栏选择：**📊 日报导出工具** → **🔧 配置管理** → **🔄 重新加载配置**
2. 或者重新运行主程序（会自动加载最新配置）

## 菜单功能说明

### 🔧 配置管理菜单

- **📝 创建配置表模板**：创建标准的配置表模板，包含默认配置数据
- **🔄 重新加载配置**：从配置表重新读取配置信息

### 其他相关功能

- **📋 查看配置信息**：显示当前加载的配置信息和来源
- **❓ 使用帮助**：查看完整的使用说明

## 配置验证

系统会自动验证配置的有效性：

- ✅ 检查表格名称和URL是否为空
- ✅ 验证URL格式是否为有效的Google Sheets链接
- ✅ 跳过无效的配置行
- ✅ 如果配置表为空或无效，自动使用默认配置

## 错误处理

- 如果"配置"表不存在，系统会使用默认的硬编码配置
- 如果配置表读取失败，会显示错误信息并回退到默认配置
- 无效的配置行会被跳过，并在控制台显示警告信息

## 注意事项

1. **URL格式**：必须是完整的Google Sheets URL，包含 `docs.google.com/spreadsheets/d/`
2. **权限要求**：确保脚本对配置中的所有表格都有访问权限
3. **配置更新**：修改配置后记得使用"重新加载配置"功能
4. **备份建议**：重要配置建议定期备份

## 故障排除

### 问题：配置表创建失败
- 检查是否有编辑当前工作簿的权限
- 确保工作簿中没有同名的"配置"工作表

### 问题：配置加载失败
- 检查"配置"工作表是否存在
- 验证配置表格式是否正确
- 查看控制台日志获取详细错误信息

### 问题：某些表格无法访问
- 确认对目标表格有查看权限
- 检查URL是否正确
- 验证表格是否已被删除或移动

## 技术实现

- 配置数据存储在当前工作簿的"配置"工作表中
- 支持动态加载和热重载配置
- 向下兼容原有的硬编码配置方式
- 包含完整的错误处理和用户反馈机制
