# 日报提取工具 - 配置表使用说明

## 概述

日报提取工具现在支持从当前Google Sheet的"配置"表中动态读取表格配置，无需修改代码即可管理源表格列表。

## 功能特点

- ✅ 从"配置"工作表动态读取表格配置
- ✅ 支持在线添加、删除、修改表格配置
- ✅ 支持启用/禁用控制（yes/no）
- ✅ 自动创建配置表模板
- ✅ 配置验证和错误处理
- ✅ 完全基于配置表，不再使用硬编码配置

## 使用步骤

### 1. 创建配置表

1. 打开包含日报提取脚本的Google Sheet
2. 在菜单栏选择：**📊 日报导出工具** → **🔧 配置管理** → **📝 创建配置表模板**
3. 系统会自动创建"配置"工作表，并填入默认配置数据

### 2. 配置表格式

配置表包含以下列：

| 列名 | 说明 | 必填 | 示例值 |
|------|------|------|--------|
| 表格名称 | 源表格的显示名称 | ✅ | NEXO-数据日报表 |
| 表格URL | 完整的Google Sheets URL | ✅ | https://docs.google.com/spreadsheets/d/... |
| 启用状态 | 是否启用该配置 | ✅ | yes / no |
| 备注 | 可选的备注信息 | ❌ | 原默认配置 |

### 3. 配置表示例

```
表格名称                | 表格URL                                                           | 启用状态 | 备注
NEXO-数据日报表        | https://docs.google.com/spreadsheets/d/12d8JKdJQG62sftuzL...    | yes     | 原默认配置
Spin777-日报表         | https://docs.google.com/spreadsheets/d/1kXlOV3vapB00bqi8A...    | yes     | 原默认配置
临时禁用的表格         | https://docs.google.com/spreadsheets/d/1ABC123...               | no      | 临时禁用
自定义表格             | https://docs.google.com/spreadsheets/d/1DEF456...               | yes     | 新增表格
```

### 4. 修改配置

1. 直接在"配置"工作表中编辑表格信息
2. 添加新行来增加表格
3. 删除行来移除表格
4. 修改URL或名称来更新配置
5. **设置启用状态**：
   - 设置为 `yes` 启用该表格
   - 设置为 `no` 临时禁用该表格（不会被处理）
   - 支持的值：yes/no, true/false, 是/否

### 5. 应用配置更改

修改配置后，需要重新加载配置：
1. 在菜单栏选择：**📊 日报导出工具** → **🔧 配置管理** → **🔄 重新加载配置**
2. 或者重新运行主程序（会自动加载最新配置）

## 菜单功能说明

### 🔧 配置管理菜单

- **📝 创建配置表模板**：创建标准的配置表模板，包含默认配置数据
- **🔄 重新加载配置**：从配置表重新读取配置信息

### 其他相关功能

- **📋 查看配置信息**：显示当前加载的配置信息和来源
- **❓ 使用帮助**：查看完整的使用说明

## 配置验证

系统会自动验证配置的有效性：

- ✅ 检查表格名称和URL是否为空
- ✅ 验证URL格式是否为有效的Google Sheets链接
- ✅ 检查启用状态，只处理启用的配置
- ✅ 跳过无效的配置行
- ✅ 如果没有有效的启用配置，会显示错误信息

## 错误处理

- 如果"配置"表不存在，系统会显示错误并要求创建配置表
- 如果配置表读取失败，会显示详细错误信息
- 无效的配置行会被跳过，并在控制台显示警告信息
- 如果没有有效的启用配置，程序会停止执行并提示用户

## 注意事项

1. **URL格式**：必须是完整的Google Sheets URL，包含 `docs.google.com/spreadsheets/d/`
2. **权限要求**：确保脚本对配置中的所有表格都有访问权限
3. **配置更新**：修改配置后记得使用"重新加载配置"功能
4. **启用状态**：第三列必须填写，支持 yes/no, true/false, 是/否
5. **备份建议**：重要配置建议定期备份
6. **必须创建配置表**：程序不再使用硬编码配置，必须先创建配置表

## 故障排除

### 问题：配置表创建失败
- 检查是否有编辑当前工作簿的权限
- 确保工作簿中没有同名的"配置"工作表

### 问题：配置加载失败
- 检查"配置"工作表是否存在
- 验证配置表格式是否正确
- 查看控制台日志获取详细错误信息

### 问题：某些表格无法访问
- 确认对目标表格有查看权限
- 检查URL是否正确
- 验证表格是否已被删除或移动

## 技术实现

- 配置数据存储在当前工作簿的"配置"工作表中
- 支持动态加载和热重载配置
- 向下兼容原有的硬编码配置方式
- 包含完整的错误处理和用户反馈机制
