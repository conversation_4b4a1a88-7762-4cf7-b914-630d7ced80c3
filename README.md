# Google Sheets 数据导出工具 - Google Apps Script版本

这是原Python项目的Google Apps Script版本，可以直接在Google环境中运行，无需本地配置。

## 🚀 快速开始

### 1. 创建Google Apps Script项目

1. 访问 [Google Apps Script](https://script.google.com/)
2. 点击"新建项目"
3. 将项目重命名为"广告数据导出工具"

### 2. 复制代码

1. 删除默认的`Code.gs`文件内容
2. 将`Code.gs`文件中的完整代码复制粘贴进去
3. 保存项目（Ctrl+S）

### 3. 授权和运行

1. 点击工具栏中的"运行"按钮，选择`main`函数
2. 首次运行会要求授权，点击"审核权限"
3. 选择您的Google账户并授权访问Google Sheets
4. 授权完成后，脚本会自动开始运行

## 📊 功能特性

### ✅ 相比Python版本的优势

- **无需本地环境**：直接在Google云端运行
- **自动权限管理**：无需配置多个服务账户
- **原生集成**：与Google Sheets完美集成
- **自动触发**：可设置定时自动运行
- **实时输出**：直接生成Google Sheets而非CSV文件

### 🔧 主要功能

- 🔗 批量处理16个预配置的Google Sheets
- 📊 自动识别所有工作表（跳过隐藏表格）
- 🔍 智能查找包含"渠道"的表头行
- 📅 筛选最近30天的数据（可配置）
- 📁 生成统一格式的汇总表格
- 📈 提供详细的处理日志和统计信息

## ⚙️ 配置说明

### 修改筛选天数

在代码中找到以下配置并修改：

```javascript
const CONFIG = {
  FILTER_DAYS: 30,  // 改为您需要的天数
  // ...
};
```

### 添加或修改表格

在`SHEET_CONFIGS`数组中添加新的表格配置：

```javascript
const SHEET_CONFIGS = [
  // 现有配置...
  {
    title: '新表格名称',
    url: 'https://docs.google.com/spreadsheets/d/YOUR_SHEET_ID/edit'
  }
];
```

## 🕐 设置定时运行

### 创建定时触发器

运行以下函数来设置每日自动运行：

```javascript
createDailyTrigger()  // 每天早上9点运行
```

### 管理触发器

- 查看触发器：在Apps Script编辑器左侧点击"触发器"
- 删除触发器：运行`deleteAllTriggers()`函数

## 📋 输出格式

脚本会创建一个新的Google Sheets，包含以下列：

| 列名 | 说明 |
|------|------|
| 日期 | 数据日期 |
| 消耗($) | 广告费用 |
| 广告系列 | 广告系列名称 |
| 渠道 | 投放渠道 |
| 使用人 | 数据使用者 |
| 包名 | 应用包名 |
| 工作表 | 来源工作表名称 |
| 来源表格 | 来源表格名称 |

## 🔍 查看运行日志

1. 在Apps Script编辑器中点击"执行"
2. 查看最近的执行记录
3. 点击执行记录查看详细日志

## ⚠️ 注意事项

### 权限要求

- 脚本需要访问Google Sheets的权限
- 确保您的Google账户可以访问所有配置的表格
- 如果某些表格无法访问，脚本会跳过并在日志中显示

### 执行限制

- Google Apps Script单次执行时间限制为6分钟
- 如果处理的表格过多，可能需要分批处理
- 建议在非高峰时段运行以获得更好的性能

### 数据格式要求

脚本会自动识别以下列名（不区分大小写）：
- **日期相关**：日期、date
- **费用相关**：消耗、cost、花费
- **渠道相关**：渠道、channel
- **用户相关**：使用人、user
- **包名相关**：包名、package
- **广告系列**：广告系列、campaign

## 🛠️ 故障排除

### 常见问题

1. **权限错误**
   - 确保已正确授权
   - 检查是否有访问目标表格的权限

2. **执行超时**
   - 减少处理的表格数量
   - 考虑分批处理

3. **数据格式问题**
   - 检查表格中是否包含"渠道"列
   - 确保日期和费用列格式正确

### 调试技巧

- 使用`console.log()`查看详细日志
- 在Apps Script编辑器中设置断点调试
- 先测试单个表格再批量处理

## 📞 技术支持

如果遇到问题，请检查：
1. Google Apps Script执行日志
2. 表格访问权限
3. 数据格式是否符合要求

---

**提示**：相比Python版本，Google Apps Script版本更适合团队协作和自动化运行，推荐在生产环境中使用。
